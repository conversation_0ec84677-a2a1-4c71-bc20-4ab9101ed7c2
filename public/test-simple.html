<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Simple GrapesJS Test</title>
    <link rel="stylesheet" href="https://unpkg.com/grapesjs/dist/css/grapes.min.css">
    <style>
        body { margin: 0; }
        #gjs { height: 100vh; }
    </style>
</head>
<body>
    <div id="gjs">
        <h1>Hello World Component!</h1>
    </div>

    <script src="https://unpkg.com/grapesjs"></script>
    <script src="https://unpkg.com/grapesjs-blocks-basic"></script>
    
    <script type="text/javascript">
        console.log('Starting GrapesJS...');
        
        const editor = grapesjs.init({
            container: '#gjs',
            fromElement: true,
            height: '100vh',
            width: 'auto',
            storageManager: false,
            
            plugins: ['gjs-blocks-basic'],
            pluginsOpts: {
                'gjs-blocks-basic': {
                    blocks: ['column1', 'column2', 'column3', 'column3-7', 'text', 'link', 'image'],
                    category: 'Layout',
                    flexGrid: 1
                }
            }
        });
        
        console.log('GrapesJS loaded!');
        console.log('Blocks:', editor.BlockManager.getAll().models.map(b => b.get('id')));
    </script>
</body>
</html>
