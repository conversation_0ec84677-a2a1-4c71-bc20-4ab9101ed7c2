<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Test Fixed Syntax</title>
    <style>
        body { margin: 0; font-family: Arial, sans-serif; }
        .status { padding: 20px; text-align: center; font-size: 18px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
    </style>
</head>
<body>
    <div id="status" class="status">Testing JavaScript syntax...</div>
    
    <script>
        try {
            // Test the exact configuration structure from the main file
            const testConfig = {
                container: '#gjs',
                height: '100%',
                width: 'auto',
                storageManager: false,

                plugins: ['gjs-blocks-basic'],
                pluginsOpts: {
                    'gjs-blocks-basic': {
                        blocks: ['column1', 'column2', 'column3', 'column3-7', 'text', 'link', 'image', 'video'],
                        category: 'Layout',
                        flexGrid: 1,
                        stylePrefix: 'gjs-',
                        addBasicStyle: true,
                        labelColumn1: '1 Column',
                        labelColumn2: '2 Columns',
                        labelColumn3: '3 Columns',
                        labelColumn37: '2 Columns 3/7',
                        labelText: 'Text',
                        labelLink: 'Link',
                        labelImage: 'Image',
                        labelVideo: 'Video',
                        rowHeight: 75,
                    }
                },

                styleManager: {
                    sectors: [{
                        name: 'General',
                        buildProps: ['float', 'display', 'position', 'top', 'right', 'left', 'bottom'],
                        properties: [{
                            name: 'Alignment',
                            property: 'float',
                            type: 'radio',
                            defaults: 'none',
                            list: [
                                { value: 'none', className: 'fa fa-times'},
                                { value: 'left', className: 'fa fa-align-left'},
                                { value: 'right', className: 'fa fa-align-right'}
                            ],
                        },{
                            property: 'position',
                            type: 'select',
                        }]
                    },{
                        name: 'Dimension',
                        open: false,
                        buildProps: ['width', 'min-height', 'padding'],
                        properties: [{
                            id: 'custom-prop',
                            name: 'Custom Label',
                            property: 'font-size',
                            type: 'select',
                            defaults: '32px',
                            list: [
                                { value: '12px', name: 'Tiny'},
                                { value: '18px', name: 'Medium'},
                                { value: '32px', name: 'Big'},
                            ],
                        }]
                    },{
                        name: 'Typography',
                        open: false,
                        buildProps: ['font-family', 'font-size', 'font-weight', 'letter-spacing', 'color', 'line-height', 'text-align', 'text-decoration', 'text-shadow'],
                        properties: [
                            { name: 'Font', property: 'font-family'},
                            { name: 'Weight', property: 'font-weight'},
                            { name: 'Font size', property: 'font-size'},
                            { name: 'Letter spacing', property: 'letter-spacing'},
                            { name: 'Color', property: 'color'},
                            { name: 'Line height', property: 'line-height'},
                            {
                                name: 'Text align',
                                property: 'text-align',
                                type: 'radio',
                                defaults: 'left',
                                list: [
                                    { value: 'left', name: 'Left', className: 'fa fa-align-left'},
                                    { value: 'center', name: 'Center', className: 'fa fa-align-center' },
                                    { value: 'right', name: 'Right', className: 'fa fa-align-right'},
                                    { value: 'justify', name: 'Justify', className: 'fa fa-align-justify'}
                                ],
                            },
                            { name: 'Text decoration', property: 'text-decoration'},
                            { name: 'Text shadow', property: 'text-shadow'}
                        ]
                    },{
                        name: 'Decorations',
                        open: false,
                        buildProps: ['opacity', 'background-color', 'border-radius', 'border', 'box-shadow', 'background'],
                        properties: [
                            { name: 'Opacity', property: 'opacity', type: 'slider', defaults: 1, step: 0.01, max: 1, min: 0},
                            { name: 'Background color', property: 'background-color'},
                            { name: 'Border radius', property: 'border-radius'},
                            { name: 'Border', property: 'border'},
                            { name: 'Box shadow', property: 'box-shadow'},
                            { name: 'Background', property: 'background'},
                        ]
                    },{
                        name: 'Extra',
                        open: false,
                        buildProps: ['transition', 'perspective', 'transform'],
                        properties: [
                            { name: 'Transition', property: 'transition'},
                            { name: 'Perspective', property: 'perspective'},
                            { name: 'Transform', property: 'transform'},
                        ]
                    }]
                },

                canvas: {
                    styles: [
                        'https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css'
                    ]
                }
            };
            
            // If we reach here, the syntax is valid
            document.getElementById('status').innerHTML = '✅ JavaScript syntax is CORRECT! No syntax errors found.';
            document.getElementById('status').className = 'status success';
            console.log('Configuration test passed:', testConfig);
            
        } catch (error) {
            document.getElementById('status').innerHTML = '❌ JavaScript syntax ERROR: ' + error.message;
            document.getElementById('status').className = 'status error';
            console.error('Syntax error:', error);
        }
    </script>
</body>
</html>
