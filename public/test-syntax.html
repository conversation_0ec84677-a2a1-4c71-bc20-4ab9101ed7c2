<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Test JavaScript Syntax</title>
    <link rel="stylesheet" href="https://unpkg.com/grapesjs/dist/css/grapes.min.css">
    <style>
        body { margin: 0; font-family: Arial, sans-serif; }
        .header { background: #2c3e50; color: white; padding: 10px; text-align: center; }
        #gjs { height: calc(100vh - 50px); }
        .error { color: red; padding: 20px; }
        .success { color: green; padding: 20px; }
    </style>
</head>
<body>
    <div class="header">
        <h2>Test JavaScript Syntax - GrapesJS</h2>
    </div>
    
    <div id="status"></div>
    <div id="gjs"></div>

    <script src="https://unpkg.com/grapesjs"></script>
    <script src="https://unpkg.com/grapesjs-blocks-basic"></script>
    
    <script type="text/javascript">
        const statusDiv = document.getElementById('status');
        
        try {
            console.log('Testing GrapesJS configuration...');
            
            // Test the exact configuration from the main file
            const editor = grapesjs.init({
                container: '#gjs',
                height: '100%',
                width: 'auto',
                storageManager: false,

                // Use only blocks-basic to get default layout columns
                plugins: ['gjs-blocks-basic'],
                pluginsOpts: {
                    'gjs-blocks-basic': {
                        // Enable all default layout blocks
                        blocks: ['column1', 'column2', 'column3', 'column3-7', 'text', 'link', 'image', 'video'],
                        category: 'Layout',
                        flexGrid: 1,
                        stylePrefix: 'gjs-',
                        addBasicStyle: true,
                        labelColumn1: '1 Column',
                        labelColumn2: '2 Columns',
                        labelColumn3: '3 Columns',
                        labelColumn37: '2 Columns 3/7',
                        labelText: 'Text',
                        labelLink: 'Link',
                        labelImage: 'Image',
                        labelVideo: 'Video',
                        rowHeight: 75,
                    }
                },

                // Style Manager configuration
                styleManager: {
                    sectors: [{
                        name: 'General',
                        buildProps: ['float', 'display', 'position', 'top', 'right', 'left', 'bottom'],
                        properties: [{
                            name: 'Alignment',
                            property: 'float',
                            type: 'radio',
                            defaults: 'none',
                            list: [
                                { value: 'none', className: 'fa fa-times'},
                                { value: 'left', className: 'fa fa-align-left'},
                                { value: 'right', className: 'fa fa-align-right'}
                            ],
                        },{
                            property: 'position',
                            type: 'select',
                        }]
                    },{
                        name: 'Dimension',
                        open: false,
                        buildProps: ['width', 'min-height', 'padding'],
                        properties: [{
                            id: 'custom-prop',
                            name: 'Custom Label',
                            property: 'font-size',
                            type: 'select',
                            defaults: '32px',
                            list: [
                                { value: '12px', name: 'Tiny'},
                                { value: '18px', name: 'Medium'},
                                { value: '32px', name: 'Big'},
                            ],
                        }]
                    },{
                        name: 'Typography',
                        open: false,
                        buildProps: ['font-family', 'font-size', 'font-weight', 'letter-spacing', 'color', 'line-height', 'text-align', 'text-decoration', 'text-shadow'],
                        properties: [
                            { name: 'Font', property: 'font-family'},
                            { name: 'Weight', property: 'font-weight'},
                            { name: 'Font size', property: 'font-size'},
                            { name: 'Letter spacing', property: 'letter-spacing'},
                            { name: 'Color', property: 'color'},
                            { name: 'Line height', property: 'line-height'},
                            {
                                name: 'Text align',
                                property: 'text-align',
                                type: 'radio',
                                defaults: 'left',
                                list: [
                                    { value: 'left', name: 'Left', className: 'fa fa-align-left'},
                                    { value: 'center', name: 'Center', className: 'fa fa-align-center' },
                                    { value: 'right', name: 'Right', className: 'fa fa-align-right'},
                                    { value: 'justify', name: 'Justify', className: 'fa fa-align-justify'}
                                ],
                            },
                            { name: 'Text decoration', property: 'text-decoration'},
                            { name: 'Text shadow', property: 'text-shadow'}
                        ]
                    },{
                        name: 'Decorations',
                        open: false,
                        buildProps: ['opacity', 'background-color', 'border-radius', 'border', 'box-shadow', 'background'],
                        properties: [
                            { name: 'Opacity', property: 'opacity', type: 'slider', defaults: 1, step: 0.01, max: 1, min: 0},
                            { name: 'Background color', property: 'background-color'},
                            { name: 'Border radius', property: 'border-radius'},
                            { name: 'Border', property: 'border'},
                            { name: 'Box shadow', property: 'box-shadow'},
                            { name: 'Background', property: 'background'},
                        ]
                    },{
                        name: 'Extra',
                        open: false,
                        buildProps: ['transition', 'perspective', 'transform'],
                        properties: [
                            { name: 'Transition', property: 'transition'},
                            { name: 'Perspective', property: 'perspective'},
                            { name: 'Transform', property: 'transform'},
                        ]
                    }]
                },

                // Canvas configuration
                canvas: {
                    styles: [
                        'https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css'
                    ]
                }
            });
            
            // Add additional layout blocks
            const blockManager = editor.BlockManager;
            
            blockManager.add('column4', {
                label: '4 Columns',
                category: 'Layout',
                content: `
                    <div class="row">
                        <div class="col-3" style="min-height: 75px; padding: 10px; border: 1px dashed #ddd;">
                            <p style="text-align: center; color: #999; margin: 20px 0;">Column 1</p>
                        </div>
                        <div class="col-3" style="min-height: 75px; padding: 10px; border: 1px dashed #ddd;">
                            <p style="text-align: center; color: #999; margin: 20px 0;">Column 2</p>
                        </div>
                        <div class="col-3" style="min-height: 75px; padding: 10px; border: 1px dashed #ddd;">
                            <p style="text-align: center; color: #999; margin: 20px 0;">Column 3</p>
                        </div>
                        <div class="col-3" style="min-height: 75px; padding: 10px; border: 1px dashed #ddd;">
                            <p style="text-align: center; color: #999; margin: 20px 0;">Column 4</p>
                        </div>
                    </div>
                `,
                attributes: { class: 'fa fa-th' }
            });
            
            statusDiv.innerHTML = '<div class="success">✅ JavaScript syntax is correct! GrapesJS loaded successfully.</div>';
            console.log('GrapesJS initialized successfully');
            console.log('Available blocks:', editor.BlockManager.getAll().models.map(block => block.get('id')));
            
        } catch (error) {
            statusDiv.innerHTML = '<div class="error">❌ JavaScript Error: ' + error.message + '</div>';
            console.error('Error:', error);
        }
    </script>
</body>
</html>
