/* Custom GrapesJS Styling */

/* Vietnamese language support */
.gjs-pn-panel .gjs-pn-btn {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Custom block categories */
.gjs-blocks-c .gjs-block-category .gjs-title {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: 600;
    padding: 10px 15px;
    border-radius: 6px 6px 0 0;
}

/* Block styling */
.gjs-block {
    border-radius: 8px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.gjs-block:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* Toolbar styling */
.gjs-toolbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.gjs-toolbar-item {
    border-radius: 4px;
    margin: 2px;
}

.gjs-toolbar-item:hover {
    background: #667eea;
    color: white;
}

/* Style manager improvements */
.gjs-sm-sector .gjs-sm-title {
    background: #f8f9fa;
    color: #495057;
    font-weight: 600;
    padding: 12px 15px;
    border-radius: 6px;
    margin-bottom: 10px;
}

.gjs-sm-property {
    padding: 8px 15px;
    border-bottom: 1px solid #e9ecef;
}

.gjs-sm-property:last-child {
    border-bottom: none;
}

/* Layer manager styling */
.gjs-lm-layer {
    padding: 8px 12px;
    border-radius: 4px;
    margin: 2px 0;
    transition: all 0.2s ease;
}

.gjs-lm-layer:hover {
    background: #f8f9fa;
}

.gjs-lm-layer.gjs-lm-open {
    background: #e3f2fd;
}

/* Canvas improvements */
.gjs-cv-canvas {
    background: #f5f5f5;
}

.gjs-frame {
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* Panel styling */
.gjs-pn-panel {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.gjs-pn-btn {
    border-radius: 6px;
    margin: 2px;
    transition: all 0.2s ease;
}

.gjs-pn-btn:hover {
    background: #667eea;
    color: white;
}

.gjs-pn-btn.gjs-pn-active {
    background: #667eea;
    color: white;
}

/* Device manager */
.gjs-pn-devices-c .gjs-pn-btn {
    border-radius: 20px;
    padding: 8px 16px;
}

/* Custom component highlighting */
.gjs-selected {
    outline: 2px solid #667eea !important;
    outline-offset: 2px;
}

.gjs-hovered {
    outline: 1px dashed #667eea !important;
    outline-offset: 1px;
}

/* Modal improvements */
.gjs-mdl-dialog {
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.gjs-mdl-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0;
    padding: 20px;
}

.gjs-mdl-content {
    padding: 20px;
}

/* Asset manager */
.gjs-am-assets {
    padding: 15px;
}

.gjs-am-asset {
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.gjs-am-asset:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Responsive design indicators */
@media (max-width: 768px) {
    .gjs-pn-panel {
        font-size: 14px;
    }
    
    .gjs-block {
        margin: 5px;
        padding: 8px;
    }
}

/* Loading states */
.gjs-editor.gjs-loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    z-index: 9999;
}

.gjs-editor.gjs-loading::after {
    content: 'Đang tải...';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 18px;
    color: #667eea;
    z-index: 10000;
}

/* Custom scrollbar */
.gjs-blocks-c::-webkit-scrollbar,
.gjs-lm-layers::-webkit-scrollbar,
.gjs-sm-sectors::-webkit-scrollbar {
    width: 8px;
}

.gjs-blocks-c::-webkit-scrollbar-track,
.gjs-lm-layers::-webkit-scrollbar-track,
.gjs-sm-sectors::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.gjs-blocks-c::-webkit-scrollbar-thumb,
.gjs-lm-layers::-webkit-scrollbar-thumb,
.gjs-sm-sectors::-webkit-scrollbar-thumb {
    background: #667eea;
    border-radius: 4px;
}

.gjs-blocks-c::-webkit-scrollbar-thumb:hover,
.gjs-lm-layers::-webkit-scrollbar-thumb:hover,
.gjs-sm-sectors::-webkit-scrollbar-thumb:hover {
    background: #5a6fd8;
}

/* Success/Error states */
.gjs-success {
    border-left: 4px solid #28a745;
    background: #d4edda;
    color: #155724;
    padding: 10px 15px;
    border-radius: 4px;
    margin: 10px 0;
}

.gjs-error {
    border-left: 4px solid #dc3545;
    background: #f8d7da;
    color: #721c24;
    padding: 10px 15px;
    border-radius: 4px;
    margin: 10px 0;
}

/* Animation classes */
.gjs-fadeIn {
    animation: fadeIn 0.3s ease-in;
}

.gjs-slideIn {
    animation: slideIn 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { 
        opacity: 0;
        transform: translateY(-20px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}
