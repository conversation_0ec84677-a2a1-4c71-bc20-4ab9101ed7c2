@extends('layouts.admin')
@push('title', '<PERSON><PERSON><PERSON><PERSON> lý học viên CRM')
@push('meta')
@endpush
@push('css')
    <style>
        .crm-stats-card {
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
            transition: all 0.3s;
            border-left: 4px solid transparent;
        }

        .crm-stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
        }

        .crm-stats-value {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }

        .crm-stats-label {
            font-size: 14px;
            color: #6c757d;
            font-weight: 500;
        }

        .stats-icon {
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .bg-primary-subtle {
            background-color: rgba(13, 110, 253, 0.1);
        }

        .bg-success-subtle {
            background-color: rgba(25, 135, 84, 0.1);
        }

        .bg-warning-subtle {
            background-color: rgba(255, 193, 7, 0.1);
        }

        .bg-info-subtle {
            background-color: rgba(13, 202, 240, 0.1);
        }

        .bg-danger-subtle {
            background-color: rgba(220, 53, 69, 0.1);
        }

        .crm-activity-card {
            border-left: 4px solid #3498db;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 4px;
            background: #f8f9fa;
        }

        .lead-new {
            border-left-color: #17a2b8;
        }

        .lead-contacted {
            border-left-color: #007bff;
        }

        .lead-nurturing {
            border-left-color: #ffc107;
        }

        .lead-qualified {
            border-left-color: #28a745;
        }

        .lead-proposal {
            border-left-color: #6f42c1;
        }

        .lead-closed-won {
            border-left-color: #28a745;
        }

        .lead-closed-lost {
            border-left-color: #dc3545;
        }

        .activity-time {
            font-size: 12px;
            color: #6c757d;
        }

        .next-follow-up {
            background-color: #ffe8d9;
            border-left: 4px solid #fd7e14;
            padding: 8px 12px;
            border-radius: 4px;
            margin-top: 10px;
        }

        /* Source Analytics Styles */
        .source-analytics-toggle {
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .source-analytics-toggle:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .source-analytics-content {
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .toggle-icon {
            transition: transform 0.3s ease;
        }

        .toggle-icon.active {
            transform: rotate(180deg);
        }

        .source-stats-table tbody tr:hover {
            background-color: rgba(13, 110, 253, 0.05);
        }

        .cursor-pointer {
            cursor: pointer;
        }

        .filter-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .filter-label {
            font-size: 12px;
            font-weight: 500;
            color: #6c757d;
            margin-bottom: 5px;
        }

        .filter-info {
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            border: 1px solid #e1bee7;
            border-radius: 6px;
        }

        .date-range-inputs {
            position: relative;
        }

        .date-range-inputs::after {
            content: "→";
            position: absolute;
            top: 50%;
            right: -15px;
            transform: translateY(-50%);
            color: #6c757d;
            font-weight: bold;
        }
    </style>
@endpush
@section('content')
    <div class="ol-card radius-8px">
        <div class="ol-card-body my-3 py-12px px-20px">
            <div class="d-flex align-items-center justify-content-between gap-3 flex-wrap flex-md-nowrap">
                <h4 class="title fs-16px">
                    <i class="fi-rr-settings-sliders me-2"></i>
                    Quản lý học viên CRM
                </h4>

                <a href="{{ route('admin.student.create') }}"
                   class="btn ol-btn-outline-secondary d-flex align-items-center cg-10px">
                    <span class="fi-rr-plus"></span>
                    <span>Thêm học viên mới</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Thống kê CRM -->
    <div class="row mt-4">
        <div class="col-md-2">
            <div class="crm-stats-card bg-white">
                <div class="d-flex align-items-center">
                    <div class="stats-icon bg-primary-subtle rounded-circle p-3 me-3">
                        <i class="fi-rr-users text-primary fs-4"></i>
                    </div>
                    <div>
                        <div class="crm-stats-value">{{ $stats['total_leads'] }}</div>
                        <div class="crm-stats-label">Tổng số khách hàng</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="crm-stats-card bg-white">
                <div class="d-flex align-items-center">
                    <div class="stats-icon bg-info-subtle rounded-circle p-3 me-3">
                        <i class="fi-rr-user-add text-info fs-4"></i>
                    </div>
                    <div>
                        <div class="crm-stats-value">{{ $stats['new_leads'] }}</div>
                        <div class="crm-stats-label">Khách hàng tiềm năng</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="crm-stats-card bg-white">
                <div class="d-flex align-items-center">
                    <div class="stats-icon bg-warning-subtle rounded-circle p-3 me-3">
                        <i class="fi-rr-comment text-warning fs-4"></i>
                    </div>
                    <div>
                        <div class="crm-stats-value">{{ $stats['contacted_leads'] }}</div>
                        <div class="crm-stats-label">Đã liên hệ</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="crm-stats-card bg-white">
                <div class="d-flex align-items-center">
                    <div class="stats-icon bg-primary-subtle rounded-circle p-3 me-3">
                        <i class="fi-rr-briefcase text-primary fs-4"></i>
                    </div>
                    <div>
                        <div class="crm-stats-value">{{ $stats['opportunities'] }}</div>
                        <div class="crm-stats-label">Đang thương lượng</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="crm-stats-card bg-white">
                <div class="d-flex align-items-center">
                    <div class="stats-icon bg-success-subtle rounded-circle p-3 me-3">
                        <i class="fi-rr-checkbox text-success fs-4"></i>
                    </div>
                    <div>
                        <div class="crm-stats-value">{{ $stats['won_deals'] }}</div>
                        <div class="crm-stats-label">Khách hàng đã chốt</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="crm-stats-card bg-white">
                <div class="d-flex align-items-center">
                    <div class="stats-icon bg-success-subtle rounded-circle p-3 me-3">
                        <i class="fi-rr-stats text-success fs-4"></i>
                    </div>
                    <div>
                        <div class="crm-stats-value">{{ $stats['conversion_rate'] }}%</div>
                        <div class="crm-stats-label">Tỷ lệ chuyển đổi</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-2 mb-4">
        <div class="col-md-6">
            <div class="crm-stats-card bg-white">
                <div class="d-flex align-items-center">
                    <div class="stats-icon bg-success-subtle rounded-circle p-3 me-3">
                        <i class="fi-rr-dollar text-success fs-4"></i>
                    </div>
                    <div>
                        <div class="crm-stats-value">{{ $stats['total_value'] }}</div>
                        <div class="crm-stats-label">Tổng doanh thu thực tế</div>
                        <small class="text-muted">Từ {{ $stats['paid_students'] }} học viên đã thanh toán</small>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="crm-stats-card bg-white">
                <div class="d-flex align-items-center">
                    <div class="stats-icon bg-info-subtle rounded-circle p-3 me-3">
                        <i class="fi-rr-chart-line-up text-info fs-4"></i>
                    </div>
                    <div>
                        <div class="crm-stats-value">{{ $stats['avg_revenue_per_student'] }}</div>
                        <div class="crm-stats-label">Doanh thu bình quân/học viên</div>
                        <small class="text-muted">Giá trị trung bình mỗi khách hàng</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Source Analytics Toggle Section -->
    <div class="row mt-4 mb-3">
        <div class="col-12">
            <div
                class="source-analytics-toggle d-flex align-items-center justify-content-between p-3 bg-white rounded shadow-sm cursor-pointer"
                id="sourceAnalyticsToggle">
                <div class="d-flex align-items-center">
                    <div class="stats-icon bg-primary-subtle rounded-circle p-3 me-3">
                        <i class="fi-rr-chart-pie-alt text-primary fs-4"></i>
                    </div>
                    <div>
                        <h5 class="mb-0 fw-bold">Phân tích nguồn đăng ký</h5>
                        <p class="text-muted mb-0">Xem thống kê từ các nguồn và tỷ lệ chuyển đổi của từng kênh</p>
                    </div>
                </div>
                <div class="toggle-icon">
                    <i class="fi-rr-angle-small-down fs-3"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Source Analytics Content (Initially Hidden) -->
    <div class="source-analytics-content bg-white p-4 rounded shadow-sm mb-4" id="sourceAnalyticsContent"
         style="display: none;">
        @if(count($source_stats) > 0)
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <p class="fw-bold mb-0">Hệ thống sẽ cập nhật sau 1 tiếng</p>
                        <div class="text-end">
                            <span class="badge bg-info rounded-pill px-3 py-2">
                                {{ count($source_stats) }} nguồn đăng ký
                            </span>
                        </div>
                    </div>
                    <hr>
                </div>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <div class="source-stats-chart-container position-relative" style="height: 300px;">
                        <canvas id="sourceStatsChart"></canvas>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="source-stats-table">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                <tr>
                                    <th>Nguồn</th>
                                    <th class="text-center">Số lượng</th>
                                    <th class="text-center">Tỷ lệ chuyển đổi</th>
                                </tr>
                                </thead>
                                <tbody>
                                @foreach($source_stats as $source)
                                    <tr>
                                        <td>
                                            <span class="d-flex align-items-center">
                                                <span class="source-icon me-2 rounded-circle"
                                                      style="width: 12px; height: 12px; background-color: {{ randomColor($loop->index) }};"></span>
                                                {{ $source['source'] }}
                                            </span>
                                        </td>
                                        <td class="text-center">{{ $source['total'] }}</td>
                                        <td class="text-center">
                                            <div class="progress" style="height: 8px;">
                                                <div class="progress-bar bg-success" role="progressbar"
                                                     style="width: {{ $source['conversion_rate'] }}%"></div>
                                            </div>
                                            <small class="mt-1 d-block">{{ $source['conversion_rate'] }}%</small>
                                        </td>
                                    </tr>
                                @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        @else
            <div class="alert alert-info">
                <i class="fi-rr-info-circle me-2"></i>
                Chưa có dữ liệu về nguồn đăng ký. Các nguồn sẽ được ghi nhận khi học viên đăng ký thông qua form nhúng
                với UTM source.
            </div>
        @endif
    </div>

    <div class="ol-card p-4">
        <div class="ol-card-body">
            <!-- Form lọc và tìm kiếm -->
            <div class="filter-section print-d-none">
                <div class="d-flex align-items-center justify-content-between mb-3">
                    <h6 class="mb-0 text-muted">
                        <i class="fi-rr-filter me-2"></i>
                        Bộ lọc tìm kiếm học viên
                    </h6>
                    @if(request()->hasAny(['search', 'lead_status', 'sale_id', 'follow_up_date', 'registration_date_from', 'registration_date_to']))
                        <span class="badge bg-primary">
                            {{ $students->total() }} kết quả
                        </span>
                    @endif
                </div>
                <form class="form-inline" action="{{ route('admin.student.index') }}" method="get">
                    <div class="row row-gap-3">
                            <div class="col-md-2">
                                <input type="text" class="form-control ol-form-control" name="search"
                                       value="{{ request('search') }}" placeholder="Tìm kiếm học viên"/>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select ol-form-control" name="lead_status">
                                    <option value="">-- Trạng thái --</option>
                                    @foreach($lead_statuses as $key => $status)
                                        <option
                                            value="{{ $key }}" {{ request('lead_status') == $key ? 'selected' : '' }}>{{ $status }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select ol-form-control" name="sale_id">
                                    <option value="">-- Nhân viên Sale --</option>
                                    @foreach($sales as $sale)
                                        <option
                                            value="{{ $sale->id }}" {{ request('sale_id') == $sale->id ? 'selected' : '' }}>{{ $sale->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <input type="date" class="form-control ol-form-control" name="follow_up_date"
                                       value="{{ request('follow_up_date') }}" placeholder="Ngày follow-up"/>
                            </div>
                        </div>
                        <div class="row row-gap-3 mt-2">
                            <div class="col-md-2">
                                <label class="form-label text-muted small">Ngày đăng ký từ:</label>
                                <input type="date" class="form-control ol-form-control" name="registration_date_from"
                                       value="{{ request('registration_date_from') }}" placeholder="Từ ngày"/>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label text-muted small">Ngày đăng ký đến:</label>
                                <input type="date" class="form-control ol-form-control" name="registration_date_to"
                                       value="{{ request('registration_date_to') }}" placeholder="Đến ngày"/>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label text-muted small">Sắp xếp theo:</label>
                                <select class="form-select ol-form-control" name="sort_by">
                                    <option value="created_at" {{ $sort_by == 'created_at' ? 'selected' : '' }}>Ngày đăng ký</option>
                                    <option value="name" {{ $sort_by == 'name' ? 'selected' : '' }}>Tên</option>
                                    <option value="email" {{ $sort_by == 'email' ? 'selected' : '' }}>Email</option>
                                    <option value="updated_at" {{ $sort_by == 'updated_at' ? 'selected' : '' }}>Cập nhật gần đây</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label text-muted small">Thứ tự:</label>
                                <select class="form-select ol-form-control" name="sort_order">
                                    <option value="desc" {{ $sort_order == 'desc' ? 'selected' : '' }}>Giảm dần</option>
                                    <option value="asc" {{ $sort_order == 'asc' ? 'selected' : '' }}>Tăng dần</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label text-muted small">&nbsp;</label>
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn ol-btn-primary flex-fill">Lọc</button>
                                    <a href="{{ route('admin.student.index') }}" class="btn ol-btn-outline-secondary">
                                        <i class="fi-rr-refresh"></i>
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label text-muted small">&nbsp;</label>
                                @if(request()->hasAny(['search', 'lead_status', 'sale_id', 'follow_up_date', 'registration_date_from', 'registration_date_to']))
                                    <div class="filter-info py-2 px-3 mb-0">
                                        <small>
                                            <i class="fi-rr-info me-1"></i>
                                            Đang áp dụng bộ lọc
                                        </small>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </form>

                    <!-- Thông tin tổng quan kết quả lọc -->
                    @if(request()->hasAny(['search', 'lead_status', 'sale_id', 'follow_up_date', 'registration_date_from', 'registration_date_to']))
                        <div class="mt-3 p-3 bg-light rounded">
                            <div class="row text-center">
                                <div class="col-md-3">
                                    <div class="text-primary fw-bold fs-5">{{ $students->total() }}</div>
                                    <small class="text-muted">Tổng kết quả</small>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-success fw-bold fs-5">{{ $students->currentPage() }}</div>
                                    <small class="text-muted">Trang hiện tại</small>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-info fw-bold fs-5">{{ $students->lastPage() }}</div>
                                    <small class="text-muted">Tổng số trang</small>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-warning fw-bold fs-5">{{ $students->perPage() }}</div>
                                    <small class="text-muted">Kết quả/trang</small>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <!-- Table -->
                    @if (count($students) > 0)
                        <div
                            class="admin-tInfo-pagi d-flex justify-content-between justify-content-center align-items-center flex-wrap gr-15">
                            <p class="admin-tInfo">
                                Hiển thị {{ count($students) }} trong tổng số {{ $students->total() }} học viên
                            </p>
                        </div>
                        <div class="table-responsive course_list" id="course_list">
                            <table class="table eTable eTable-2 print-table">
                                <thead>
                                <tr>
                                    <th scope="col">#</th>
                                    <th scope="col">Học viên</th>
                                    <th scope="col">Điện thoại</th>
                                    <th scope="col">Trạng thái</th>
                                    <th scope="col">Follow-up tiếp theo</th>
                                    <th scope="col">Khóa học đã đăng ký</th>
                                    <th scope="col">Doanh thu</th>
                                    <th scope="col">Tài khoản</th>
                                    <th scope="col">Ngày đăng ký</th>
                                    <th class="print-d-none" scope="col">Tùy chọn</th>
                                </tr>
                                </thead>
                                <tbody>
                                @foreach ($students as $key => $row)
                                    <tr>
                                        <th scope="row">
                                            <p class="row-number">
                                                @if($sort_order == 'desc')
                                                    {{ $students->total() - ($students->currentPage() - 1) * $students->perPage() - $key }}
                                                @else
                                                    {{ ($students->currentPage() - 1) * $students->perPage() + $key + 1 }}
                                                @endif
                                            </p>
                                        </th>
                                        <td>
                                            <div class="dAdmin_profile d-flex align-items-center min-w-200px">
                                                <div class="dAdmin_profile_img">
                                                    <img class="img-fluid rounded-circle image-45" width="45"
                                                         height="45" src="{{ get_image($row->photo) }}"/>
                                                </div>
                                                <div class="ms-1">
                                                    <h4 class="title fs-14px">{{ $row->name }}</h4>
                                                    <p class="sub-title2 text-12px">{{ $row->email }}</p>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="dAdmin_info_name min-w-150px">
                                                <p>{{ $row->phone }}</p>
                                            </div>
                                        </td>
                                        <td>
                                            @php
                                                $leadStatus = $row->getLatestLeadStatus();
                                                $leadStatusText = \App\Models\CrmActivity::getLeadStatusOptions()[$leadStatus] ?? 'Mới';
                                                $leadStatusClass = '';

                                                switch($leadStatus) {
                                                    case 'new': $leadStatusClass = 'bg-info'; break;
                                                    case 'contacted': $leadStatusClass = 'bg-primary'; break;
                                                    case 'nurturing': $leadStatusClass = 'bg-warning'; break;
                                                    case 'qualified': $leadStatusClass = 'bg-success'; break;
                                                    case 'proposal': $leadStatusClass = 'bg-purple'; break;
                                                    case 'closed_won': $leadStatusClass = 'bg-success'; break;
                                                    case 'closed_lost': $leadStatusClass = 'bg-danger'; break;
                                                    default: $leadStatusClass = 'bg-secondary';
                                                }
                                            @endphp
                                            <span class="badge {{ $leadStatusClass }}">{{ $leadStatusText }}</span>
                                        </td>
                                        <td>
                                            @php
                                                $nextFollowUp = $row->getNextFollowUp();
                                            @endphp
                                            @if($nextFollowUp)
                                                <div class="next-follow-up">
                                                    {{ \Carbon\Carbon::parse($nextFollowUp->next_follow_up)->format('d/m/Y H:i') }}
                                                    <div class="small">{{ $nextFollowUp->getActivityTypeText() }}</div>
                                                </div>
                                            @else
                                                <span class="text-muted">Chưa có lịch</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="d-flex flex-column">
                                                <a href="{{ route('admin.student.enrollments', $row->id) }}"
                                                   class="text-primary mb-1">
                                                    {{ App\Models\Enrollment::where('user_id', $row->id)->count() }}
                                                    khóa học
                                                </a>
                                                <a href="{{ route('admin.student.learning.progress', $row->id) }}"
                                                   class="text-success small">
                                                    <i class="fi-rr-chart-line-up me-1"></i>Xem lộ trình
                                                </a>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex flex-column">
                                                @if($row->total_revenue > 0)
                                                    <span class="fw-bold text-success">
                                                        {{ number_format($row->total_revenue, 0, ',', '.') }} VNĐ
                                                    </span>
                                                    <small class="text-muted">{{ $row->payment_count }} giao dịch</small>
                                                @else
                                                    <span class="text-muted">Chưa có doanh thu</span>
                                                @endif
                                            </div>
                                        </td>
                                        <td>
                                            @if(($row->status ?? 0) == 1)
                                                <span class="badge bg-success">Đang hoạt động</span>
                                            @else
                                                <span class="badge bg-danger">Đã khóa</span>
                                            @endif
                                            <div class="small">Vi phạm: {{ $row->account_violations ?? 0 }}</div>
                                        </td>
                                        <td>
                                            <div class="d-flex flex-column">
                                                <span class="fw-bold">{{ $row->created_at->format('d/m/Y') }}</span>
                                                <small class="text-muted">{{ $row->created_at->format('H:i') }}</small>
                                                <small class="text-muted">{{ $row->created_at->diffForHumans() }}</small>
                                            </div>
                                        </td>
                                        <td class="print-d-none">
                                            <div class="text-center admin-user-td-btn dropdown">
                                                    <span class="dropend dropend-reverce">
                                                        <button class="btn dropdown-toggle admin-user-btn px-2 py-0"
                                                                type="button" id="dropdownMenuButton1"
                                                                data-bs-toggle="dropdown" aria-expanded="false">
                                                            <i class="fi-rr-menu-dots"></i>
                                                        </button>
                                                        <ul class="dropdown-menu admin-user-dropdown"
                                                            aria-labelledby="dropdownMenuButton1">
                                                            <li>
                                                                <a class="dropdown-item" href="javascript:void(0)"
                                                                   onclick="openCrmActivityModal({{ $row->id }}, '{{ $row->name }}')">Ghi nhận tương tác</a>
                                                            </li>
                                                            <li>
                                                                <a class="dropdown-item" href="javascript:void(0)"
                                                                   onclick="openCrmOpportunityModal({{ $row->id }}, '{{ $row->name }}')">Tạo đơn hàng</a>
                                                            </li>
                                                            <li>
                                                                <a class="dropdown-item" href="javascript:void(0)"
                                                                   onclick="viewCrmHistory({{ $row->id }}, '{{ $row->name }}')">Lịch sử tiếp cận</a>
                                                            </li>
                                                            <li><hr class="dropdown-divider"></li>
                                                            <li>
                                                                <a class="dropdown-item"
                                                                   href="{{ route('admin.student.edit', $row->id) }}">Chỉnh sửa học viên</a>
                                                            </li>
                                                            <li>
                                                                <a class="dropdown-item"
                                                                   href="{{ route('admin.student.devices', $row->id) }}">Quản lý thiết bị
                                                                    @if(($row->account_violations ?? 0) > 0)
                                                                        <span
                                                                            class="badge bg-danger">{{ $row->account_violations }}</span>
                                                                    @endif
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a class="dropdown-item"
                                                                   href="{{ route('admin.student.learning.progress', $row->id) }}">
                                                                    <i class="fi-rr-chart-line-up me-2"></i>Lộ trình học
                                                                </a>
                                                            </li>
                                                            <li>
                                                            @if($row->status == 1)
                                                                    <a class="dropdown-item"
                                                                       onclick="confirmModal('{{ route('admin.student.status', ['status' => 0, 'id' => (int)$row->id]) }}')"
                                                                       href="javascript:void(0)">Khóa tài khoản</a>
                                                                @else
                                                                    <a class="dropdown-item"
                                                                       onclick="confirmModal('{{ route('admin.student.status', ['status' => 1, 'id' => (int)$row->id]) }}')"
                                                                       href="javascript:void(0)">Kích hoạt tài khoản</a>
                                                                @endif
                                                            </li>
                                                            <li>
                                                                <a class="dropdown-item"
                                                                   href="{{ route('admin.student.delete', $row->id) }}"
                                                                   onclick="return confirm('Bạn có chắc chắn muốn xóa học viên này không?')">
                                                                    Xóa học viên</a>
                                                            </li>
                                                        </ul>
                                                    </span>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        @include('admin.no_data')
                    @endif

                    <!-- Data info and Pagination -->
                    @if (count($students) > 0)
                        <div
                            class="admin-tInfo-pagi d-flex justify-content-between justify-content-center align-items-center flex-wrap gr-15">
                            <p class="admin-tInfo">
                                {{ get_phrase('Showing') . ' ' . count($students) . ' ' . get_phrase('of') . ' ' . $students->total() . ' ' . get_phrase('data') }}
                            </p>
                            {{ $students->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

@endsection


@push('js')
    <!-- Modal thêm hoạt động CRM -->
    <div class="modal fade" id="crmActivityModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Ghi nhận tương tác - <span id="studentNameActivity"></span></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="crmActivityForm">
                        <input type="hidden" id="activityStudentId" name="user_id">

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="activityType" class="form-label">Loại hoạt động</label>
                                <select class="form-select" id="activityType" name="activity_type" required>
                                    @foreach($activity_types as $key => $value)
                                        <option value="{{ $key }}">{{ $value }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="activityResult" class="form-label">Kết quả</label>
                                <select class="form-select" id="activityResult" name="result" required>
                                    @foreach($activity_results as $key => $value)
                                        <option value="{{ $key }}">{{ $value }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="activityDescription" class="form-label">Mô tả</label>
                            <textarea class="form-control" id="activityDescription" name="description" rows="3"
                                      required></textarea>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="leadStatus" class="form-label">Trạng thái lead</label>
                                <select class="form-select" id="leadStatus" name="lead_status" required>
                                    @foreach($lead_statuses as $key => $value)
                                        <option value="{{ $key }}">{{ $value }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="nextFollowUp" class="form-label">Lịch hẹn tiếp theo</label>
                                <input type="datetime-local" class="form-control" id="nextFollowUp"
                                       name="next_follow_up">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                    <button type="button" class="btn btn-primary" id="saveActivityBtn">Lưu hoạt động</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal thêm cơ hội kinh doanh -->
    <div class="modal fade" id="crmOpportunityModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Tạo đơn hàng - <span id="studentNameOpportunity"></span></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="crmOpportunityForm">
                        <input type="hidden" id="opportunityStudentId" name="user_id">

                        <div class="mb-3">
                            <label for="opportunityName" class="form-label">Tên cơ hội</label>
                            <input type="text" class="form-control" id="opportunityName" name="name" required>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="potentialValue" class="form-label">Giá trị tiềm năng (VNĐ)</label>
                                <input type="number" class="form-control" id="potentialValue" name="potential_value"
                                       required>
                            </div>
                            <div class="col-md-6">
                                <label for="probability" class="form-label">Xác suất thành công (%)</label>
                                <input type="number" class="form-control" id="probability" name="probability" min="0"
                                       max="100" required>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="opportunityNotes" class="form-label">Ghi chú</label>
                            <textarea class="form-control" id="opportunityNotes" name="notes" rows="3"></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="expectedCloseDate" class="form-label">Ngày dự kiến kết thúc</label>
                            <input type="date" class="form-control" id="expectedCloseDate" name="expected_close_date"
                                   required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                    <button type="button" class="btn btn-primary" id="saveOpportunityBtn">Lưu cơ hội</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal xem lịch sử CRM -->
    <div class="modal fade" id="crmHistoryModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Lịch sử tiếp cận - <span id="studentNameHistory"></span></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="crmHistoryTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="activities-tab" data-bs-toggle="tab"
                                    data-bs-target="#activities" type="button" role="tab" aria-controls="activities"
                                    aria-selected="true">Hoạt động
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="opportunities-tab" data-bs-toggle="tab"
                                    data-bs-target="#opportunities" type="button" role="tab"
                                    aria-controls="opportunities" aria-selected="false">Cơ hội
                            </button>
                        </li>
                    </ul>
                    <div class="tab-content pt-3" id="crmHistoryTabContent">
                        <div class="tab-pane fade show active" id="activities" role="tabpanel"
                             aria-labelledby="activities-tab">
                            <div id="activitiesList" class="p-2">
                                <div class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Đang tải...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="opportunities" role="tabpanel"
                             aria-labelledby="opportunities-tab">
                            <div id="opportunitiesList" class="p-2">
                                <div class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Đang tải...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Source Analytics Toggle
            const toggle = document.getElementById('sourceAnalyticsToggle');
            const content = document.getElementById('sourceAnalyticsContent');
            const toggleIcon = toggle.querySelector('.toggle-icon i');
            let chartInitialized = false;
            let sourceChart = null;

            // Toggle function
            toggle.addEventListener('click', function() {
                if (content.style.display === 'none') {
                    content.style.display = 'block';
                    toggleIcon.classList.add('active');

                    // Initialize chart if data exists and chart hasn't been initialized yet
                    @if(count($source_stats) > 0)
                        if (!chartInitialized) {
                            // Use requestAnimationFrame to avoid blocking the UI
                            requestAnimationFrame(() => {
                                setTimeout(() => {
                                    initSourceChart();
                                    chartInitialized = true;
                                }, 100); // Small delay to ensure DOM is ready
                            });
                        }
                    @endif
                } else {
                    content.style.display = 'none';
                    toggleIcon.classList.remove('active');
                }
            });

            // Clean up chart resources when page is hidden/unloaded
            document.addEventListener('visibilitychange', function() {
                if (document.visibilityState === 'hidden' && sourceChart) {
                    sourceChart.destroy();
                    chartInitialized = false;
                }
            });

            // Resize handler with debounce to improve performance
            let resizeTimeout;
            window.addEventListener('resize', function() {
                clearTimeout(resizeTimeout);
                resizeTimeout = setTimeout(function() {
                    if (sourceChart && chartInitialized) {
                        sourceChart.resize();
                    }
                }, 250);
            });

            // Chart initialization with performance optimizations
            function initSourceChart() {
                const ctx = document.getElementById('sourceStatsChart').getContext('2d');

                // Data from controller
                const sourceData = @json($source_stats);

                // Prepare data for chart
                const labels = sourceData.map(item => item.source);
                const totals = sourceData.map(item => item.total);
                const conversionRates = sourceData.map(item => item.conversion_rate);

                // Generate colors
                const backgroundColors = sourceData.map((_, index) => generateChartColor(index, 0.7));

                // Destroy previous chart instance if exists to prevent memory leaks
                if (sourceChart) {
                    sourceChart.destroy();
                }

                // Create chart with optimized options
                sourceChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: labels,
                        datasets: [
                            {
                                label: 'Số lượng đăng ký',
                                data: totals,
                                backgroundColor: backgroundColors,
                                borderColor: backgroundColors.map(color => color.replace('0.7', '1')),
                                borderWidth: 1
                            },
                            {
                                label: 'Tỷ lệ chuyển đổi (%)',
                                data: conversionRates,
                                type: 'line',
                                borderColor: '#4361ee',
                                borderWidth: 2,
                                fill: false,
                                tension: 0.3,
                                pointBackgroundColor: '#4361ee',
                                yAxisID: 'y1'
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        animation: {
                            duration: sourceData.length > 10 ? 0 : 1000 // Disable animation for large datasets
                        },
                        elements: {
                            point: {
                                radius: sourceData.length > 10 ? 2 : 3, // Smaller points for large datasets
                                hoverRadius: 5
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: 'Số học viên'
                                }
                            },
                            y1: {
                                beginAtZero: true,
                                position: 'right',
                                title: {
                                    display: true,
                                    text: 'Tỷ lệ chuyển đổi (%)'
                                },
                                grid: {
                                    drawOnChartArea: false
                                },
                                max: 100
                            }
                        },
                        interaction: {
                            mode: 'index',
                            intersect: false
                        },
                        plugins: {
                            title: {
                                display: true,
                                text: 'Phân tích hiệu quả các kênh marketing',
                                font: {
                                    size: 16,
                                    weight: 'bold'
                                }
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const label = context.dataset.label || '';
                                        const value = context.parsed.y;
                                        return `${label}: ${value}${context.datasetIndex === 1 ? '%' : ''}`;
                                    }
                                }
                            },
                            decimation: {
                                enabled: sourceData.length > 50, // Enable decimation for very large datasets
                                algorithm: 'min-max'
                            }
                        },
                        devicePixelRatio: 2 // Optimize for high-DPI displays
                    }
                });
            }

            // Generate chart colors
            function generateChartColor(index, opacity = 1) {
                const colors = [
                    `rgba(54, 162, 235, ${opacity})`,
                    `rgba(255, 99, 132, ${opacity})`,
                    `rgba(75, 192, 192, ${opacity})`,
                    `rgba(255, 159, 64, ${opacity})`,
                    `rgba(153, 102, 255, ${opacity})`,
                    `rgba(255, 205, 86, ${opacity})`,
                    `rgba(201, 203, 207, ${opacity})`,
                    `rgba(255, 99, 71, ${opacity})`,
                    `rgba(46, 204, 113, ${opacity})`,
                    `rgba(142, 68, 173, ${opacity})`
                ];

                return colors[index % colors.length];
            }
        });
    </script>
    <script>
        // Mở modal thêm hoạt động CRM
        function openCrmActivityModal(studentId, studentName) {
            document.getElementById('activityStudentId').value = studentId;
            document.getElementById('studentNameActivity').textContent = studentName;

            // Reset form
            document.getElementById('crmActivityForm').reset();

            // Mở modal
            const modal = new bootstrap.Modal(document.getElementById('crmActivityModal'));
            modal.show();
        }

        // Lưu hoạt động CRM
        document.getElementById('saveActivityBtn').addEventListener('click', function () {
            const form = document.getElementById('crmActivityForm');
            const formData = new FormData(form);

            fetch('{{ route("admin.crm.store.activity") }}', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'Accept': 'application/json'
                }
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Hoạt động đã được lưu thành công');
                        bootstrap.Modal.getInstance(document.getElementById('crmActivityModal')).hide();

                        // Làm mới trang để cập nhật dữ liệu
                        window.location.reload();
                    } else {
                        alert('Có lỗi xảy ra: ' + JSON.stringify(data.errors));
                    }
                })
                .catch(error => {
                    alert('Đã xảy ra lỗi: ' + error);
                });
        });

        // Mở modal thêm cơ hội kinh doanh
        function openCrmOpportunityModal(studentId, studentName) {
            document.getElementById('opportunityStudentId').value = studentId;
            document.getElementById('studentNameOpportunity').textContent = studentName;

            // Reset form
            document.getElementById('crmOpportunityForm').reset();

            // Mở modal
            const modal = new bootstrap.Modal(document.getElementById('crmOpportunityModal'));
            modal.show();
        }

        // Lưu cơ hội kinh doanh
        document.getElementById('saveOpportunityBtn').addEventListener('click', function () {
            const form = document.getElementById('crmOpportunityForm');
            const formData = new FormData(form);

            fetch('{{ route("admin.crm.store.opportunity") }}', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'Accept': 'application/json'
                }
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Cơ hội kinh doanh đã được lưu thành công');
                        bootstrap.Modal.getInstance(document.getElementById('crmOpportunityModal')).hide();

                        // Làm mới trang để cập nhật dữ liệu
                        window.location.reload();
                    } else {
                        alert('Có lỗi xảy ra: ' + JSON.stringify(data.errors));
                    }
                })
                .catch(error => {
                    alert('Đã xảy ra lỗi: ' + error);
                });
        });

        // Xem lịch sử CRM
        function viewCrmHistory(studentId, studentName) {
            document.getElementById('studentNameHistory').textContent = studentName;
            document.getElementById('studentNameHistory').dataset.studentId = studentId;

            // Mở modal
            const modal = new bootstrap.Modal(document.getElementById('crmHistoryModal'));
            modal.show();

            // Tải lịch sử hoạt động
            loadActivities(studentId);

            // Tải lịch sử cơ hội khi chuyển tab
            document.getElementById('opportunities-tab').addEventListener('click', function () {
                if (!document.getElementById('opportunitiesList').dataset.loaded) {
                    loadOpportunities(studentId);
                }
            });
        }

        // Tải lịch sử hoạt động
        function loadActivities(studentId) {
            fetch('{{ route("admin.crm.get.activities", ["userId" => "__USER_ID__"]) }}'.replace('__USER_ID__', studentId), {
                method: 'GET',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'Accept': 'application/json'
                }
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const container = document.getElementById('activitiesList');
                        container.innerHTML = '';
                        container.dataset.loaded = 'true';

                        if (data.activities.length === 0) {
                            container.innerHTML = '<div class="text-center py-3">Chưa có hoạt động nào</div>';
                            return;
                        }

                        data.activities.forEach(activity => {
                            const activityDate = new Date(activity.created_at);
                            const followUpDate = activity.next_follow_up ? new Date(activity.next_follow_up) : null;

                            const activityHtml = `
                        <div class="crm-activity-card lead-${activity.lead_status}">
                            <div class="d-flex justify-content-between">
                                <strong>${activity.activity_type === 'call' ? '📞' : activity.activity_type === 'email' ? '📧' : activity.activity_type === 'meeting' ? '👥' : activity.activity_type === 'message' ? '💬' : '📝'}
                                ${activity.sale ? activity.sale.name : 'Unknown'}</strong>
                                <span class="badge ${activity.result === 'success' ? 'bg-success' : activity.result === 'pending' ? 'bg-warning' : activity.result === 'failed' ? 'bg-danger' : 'bg-secondary'}">
                                    ${activity.result}
                                </span>
                            </div>
                            <div class="activity-time">${activityDate.toLocaleDateString('vi-VN')} ${activityDate.toLocaleTimeString('vi-VN')}</div>
                            <div class="mt-2">${activity.description}</div>
                            <div class="d-flex justify-content-between mt-2">
                                <span class="badge ${activity.lead_status === 'new' ? 'bg-info' : activity.lead_status === 'contacted' ? 'bg-primary' : activity.lead_status === 'nurturing' ? 'bg-warning' : activity.lead_status === 'qualified' ? 'bg-success' : activity.lead_status === 'proposal' ? 'bg-purple' : activity.lead_status === 'closed_won' ? 'bg-success' : 'bg-danger'}">
                                    ${activity.lead_status}
                                </span>
                                ${followUpDate ? `<span class="badge bg-warning">Follow-up: ${followUpDate.toLocaleDateString('vi-VN')} ${followUpDate.toLocaleTimeString('vi-VN')}</span>` : ''}
                            </div>
                        </div>
                    `;

                            container.innerHTML += activityHtml;
                        });
                    } else {
                        document.getElementById('activitiesList').innerHTML = '<div class="text-center py-3">Lỗi tải dữ liệu</div>';
                    }
                })
                .catch(error => {
                    document.getElementById('activitiesList').innerHTML = '<div class="text-center py-3">Lỗi tải dữ liệu: ' + error + '</div>';
                });
        }

        // Tải lịch sử cơ hội
        function loadOpportunities(studentId) {
            fetch('{{ route("admin.crm.get.opportunities", ["userId" => "__USER_ID__"]) }}'.replace('__USER_ID__', studentId), {
                method: 'GET',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'Accept': 'application/json'
                }
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const container = document.getElementById('opportunitiesList');
                        container.innerHTML = '';
                        container.dataset.loaded = 'true';

                        if (data.opportunities.length === 0) {
                            container.innerHTML = '<div class="text-center py-3">Chưa có cơ hội kinh doanh nào</div>';
                            return;
                        }

                        data.opportunities.forEach(opportunity => {
                            const createdDate = new Date(opportunity.created_at);
                            const expectedCloseDate = opportunity.expected_close_date ? new Date(opportunity.expected_close_date) : null;

                            const opportunityHtml = `
                        <div class="card mb-3">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">${opportunity.name}</h5>
                                <span class="badge ${opportunity.status === 'open' ? 'bg-primary' : opportunity.status === 'won' ? 'bg-success' : opportunity.status === 'lost' ? 'bg-danger' : 'bg-warning'}">
                                    ${opportunity.status}
                                </span>
                            </div>
                            <div class="card-body">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Tạo bởi: ${opportunity.sale ? opportunity.sale.name : 'Unknown'}</span>
                                    <span>${createdDate.toLocaleDateString('vi-VN')}</span>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-6">
                                        <strong>Giá trị:</strong> ${new Intl.NumberFormat('vi-VN', {
                                style: 'currency',
                                currency: 'VND'
                            }).format(opportunity.potential_value)}
                                    </div>
                                    <div class="col-6">
                                        <strong>Xác suất:</strong> ${opportunity.probability}%
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <strong>Ngày dự kiến kết thúc:</strong> ${expectedCloseDate ? expectedCloseDate.toLocaleDateString('vi-VN') : 'N/A'}
                                </div>
                                ${opportunity.notes ? `<div class="mb-2"><strong>Ghi chú:</strong> ${opportunity.notes}</div>` : ''}
                                ${opportunity.lost_reason ? `<div class="mb-2 text-danger"><strong>Lý do thất bại:</strong> ${opportunity.lost_reason}</div>` : ''}

                                ${opportunity.status === 'open' ? `
                                <div class="d-flex justify-content-end mt-3">
                                    <button class="btn btn-sm btn-success me-2" onclick="updateOpportunityStatus(${opportunity.id}, 'won')">Đánh dấu thành công</button>
                                    <button class="btn btn-sm btn-danger" onclick="updateOpportunityStatus(${opportunity.id}, 'lost')">Đánh dấu thất bại</button>
                                </div>
                                ` : ''}
                            </div>
                        </div>
                    `;

                            container.innerHTML += opportunityHtml;
                        });
                    } else {
                        document.getElementById('opportunitiesList').innerHTML = '<div class="text-center py-3">Lỗi tải dữ liệu</div>';
                    }
                })
                .catch(error => {
                    document.getElementById('opportunitiesList').innerHTML = '<div class="text-center py-3">Lỗi tải dữ liệu: ' + error + '</div>';
                });
        }

        // Cập nhật trạng thái cơ hội
        function updateOpportunityStatus(opportunityId, status) {
            let lostReason = '';

            if (status === 'lost') {
                lostReason = prompt('Vui lòng nhập lý do thất bại:');
                if (lostReason === null) return; // Người dùng đã hủy
            }

            const formData = new FormData();
            formData.append('_method', 'PUT');
            formData.append('status', status);
            if (lostReason) formData.append('lost_reason', lostReason);

            fetch('{{ route("admin.crm.update.opportunity.status", ["id" => "__ID__"]) }}'.replace('__ID__', opportunityId), {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'Accept': 'application/json'
                }
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Trạng thái cơ hội đã được cập nhật');

                        // Làm mới danh sách cơ hội
                        const studentId = document.getElementById('studentNameHistory').dataset.studentId;
                        loadOpportunities(studentId);

                        // Làm mới trang sau khi đóng modal
                        document.getElementById('crmHistoryModal').addEventListener('hidden.bs.modal', function () {
                            window.location.reload();
                        });
                    } else {
                        alert('Có lỗi xảy ra: ' + JSON.stringify(data.errors));
                    }
                })
                .catch(error => {
                    alert('Đã xảy ra lỗi: ' + error);
                });
        }
    </script>
@endpush

<?php
// Helper function to generate random colors for the source items
function randomColor($index)
{
    $colors = [
        '#4361ee', '#3a0ca3', '#f72585', '#4cc9f0', '#4895ef',
        '#560bad', '#f15bb5', '#00b4d8', '#9d0208', '#06d6a0'
    ];

    return $colors[$index % count($colors)];
}
?>
