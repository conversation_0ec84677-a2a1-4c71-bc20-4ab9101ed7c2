@extends('layouts.admin')
@push('title', 'Debug Doanh Thu')
@push('meta')@endpush
@push('css')
<style>
    .debug-card {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .revenue-source {
        border-left: 4px solid #007bff;
        padding: 15px;
        margin-bottom: 15px;
        background: #f8f9fa;
        border-radius: 5px;
    }

    .revenue-source.online { border-left-color: #28a745; }
    .revenue-source.offline { border-left-color: #ffc107; }
    .revenue-source.enrollment { border-left-color: #17a2b8; }

    .issue-alert {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 5px;
        padding: 15px;
        margin-bottom: 15px;
    }

    .comparison-table {
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .method-badge {
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 12px;
        font-weight: bold;
    }

    .method-online { background: #d4edda; color: #155724; }
    .method-offline { background: #fff3cd; color: #856404; }
    .method-enrollment { background: #d1ecf1; color: #0c5460; }
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="debug-card">
        <div class="d-flex align-items-center justify-content-between">
            <div>
                <h2 class="mb-2">🔍 Debug Doanh Thu Hệ Thống</h2>
                <p class="text-muted mb-0">Kiểm tra và rà soát tính chính xác của doanh thu từ tất cả nguồn</p>
            </div>
            <div class="text-end">
                <span class="badge bg-info fs-6">{{ now()->format('d/m/Y H:i') }}</span>
            </div>
        </div>
    </div>

    <!-- Tổng quan doanh thu -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="debug-card text-center">
                <div class="text-success fs-1 mb-2">
                    <i class="fi-rr-dollar"></i>
                </div>
                <h3 class="text-success">{{ number_format($revenue_data['total'], 0, ',', '.') }}</h3>
                <p class="text-muted mb-0">Tổng doanh thu (VNĐ)</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="debug-card text-center">
                <div class="text-primary fs-1 mb-2">
                    <i class="fi-rr-credit-card"></i>
                </div>
                <h3 class="text-primary">{{ number_format($revenue_data['online'], 0, ',', '.') }}</h3>
                <p class="text-muted mb-0">Thanh toán online</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="debug-card text-center">
                <div class="text-warning fs-1 mb-2">
                    <i class="fi-rr-bank"></i>
                </div>
                <h3 class="text-warning">{{ number_format($revenue_data['offline'], 0, ',', '.') }}</h3>
                <p class="text-muted mb-0">Thanh toán offline</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="debug-card text-center">
                <div class="text-info fs-1 mb-2">
                    <i class="fi-rr-graduation-cap"></i>
                </div>
                <h3 class="text-info">{{ number_format($revenue_data['free_enrollment'], 0, ',', '.') }}</h3>
                <p class="text-muted mb-0">Enrollment ước tính</p>
            </div>
        </div>
    </div>

    <!-- Cảnh báo vấn đề dữ liệu -->
    @if(count($data_issues) > 0)
    <div class="debug-card">
        <h5 class="text-danger mb-3">
            <i class="fi-rr-triangle-warning me-2"></i>
            Phát hiện vấn đề dữ liệu
        </h5>
        @foreach($data_issues as $issue)
        <div class="issue-alert">
            <i class="fi-rr-info me-2"></i>
            {{ $issue }}
        </div>
        @endforeach
    </div>
    @endif

    <!-- Chi tiết theo nguồn doanh thu -->
    <div class="row">
        <div class="col-md-6">
            <div class="debug-card">
                <h5 class="mb-3">
                    <i class="fi-rr-chart-pie me-2"></i>
                    Phân tích theo phương thức thanh toán
                </h5>
                
                <div class="revenue-source online">
                    <h6 class="text-success">Thanh toán Online</h6>
                    <div class="row">
                        @if(isset($revenue_breakdown['by_payment_method']['online']) && count($revenue_breakdown['by_payment_method']['online']) > 0)
                            @foreach($revenue_breakdown['by_payment_method']['online'] as $method => $amount)
                            <div class="col-md-6">
                                <div class="d-flex justify-content-between">
                                    <span class="method-badge method-online">{{ ucfirst($method) }}</span>
                                    <strong>{{ number_format($amount, 0, ',', '.') }} VNĐ</strong>
                                </div>
                            </div>
                            @endforeach
                        @else
                            <div class="col-12">
                                <p class="text-muted mb-0">Chưa có dữ liệu thanh toán online</p>
                            </div>
                        @endif
                    </div>
                </div>

                <div class="revenue-source offline">
                    <h6 class="text-warning">Thanh toán Offline</h6>
                    <div class="d-flex justify-content-between">
                        <span class="method-badge method-offline">Chuyển khoản</span>
                        <strong>{{ number_format($revenue_breakdown['by_payment_method']['offline'], 0, ',', '.') }} VNĐ</strong>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="debug-card">
                <h5 class="mb-3">
                    <i class="fi-rr-stats me-2"></i>
                    Top khóa học theo doanh thu
                </h5>
                
                @if(count($revenue_breakdown['by_course']) > 0)
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Khóa học</th>
                                    <th class="text-center">Online</th>
                                    <th class="text-center">Offline</th>
                                    <th class="text-center">Tổng</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach(array_slice($revenue_breakdown['by_course'], 0, 10) as $courseId => $data)
                                <tr>
                                    <td>
                                        <div class="text-truncate" style="max-width: 200px;" title="{{ $data['title'] }}">
                                            {{ $data['title'] }}
                                        </div>
                                    </td>
                                    <td class="text-center">{{ number_format($data['online'], 0, ',', '.') }}</td>
                                    <td class="text-center">{{ number_format($data['offline'], 0, ',', '.') }}</td>
                                    <td class="text-center">
                                        <strong>{{ number_format($data['total'], 0, ',', '.') }}</strong>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <p class="text-muted">Chưa có dữ liệu doanh thu theo khóa học</p>
                @endif
            </div>
        </div>
    </div>

    <!-- Doanh thu theo tháng -->
    <div class="debug-card">
        <h5 class="mb-3">
            <i class="fi-rr-calendar me-2"></i>
            Xu hướng doanh thu 12 tháng gần đây
        </h5>
        
        <div class="row">
            @foreach($revenue_breakdown['by_month'] as $monthData)
            <div class="col-md-2 mb-3">
                <div class="text-center p-3 bg-light rounded">
                    <div class="fw-bold text-primary">{{ $monthData['month'] }}</div>
                    <div class="small text-success">Online: {{ number_format($monthData['online'], 0, ',', '.') }}</div>
                    <div class="small text-warning">Offline: {{ number_format($monthData['offline'], 0, ',', '.') }}</div>
                    <div class="fw-bold mt-2">{{ number_format($monthData['total'], 0, ',', '.') }}</div>
                </div>
            </div>
            @endforeach
        </div>
    </div>

    <!-- Thông tin hoa hồng affiliate -->
    <div class="debug-card">
        <h5 class="mb-3">
            <i class="fi-rr-users me-2"></i>
            Thông tin Affiliate
        </h5>
        
        <div class="row">
            <div class="col-md-4">
                <div class="text-center p-3 bg-light rounded">
                    <div class="text-danger fs-4">{{ number_format($revenue_breakdown['affiliate_commission'], 0, ',', '.') }}</div>
                    <div class="small text-muted">Tổng hoa hồng đã chi</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="text-center p-3 bg-light rounded">
                    <div class="text-success fs-4">{{ number_format($net_revenue, 0, ',', '.') }}</div>
                    <div class="small text-muted">Doanh thu thực (sau trừ hoa hồng)</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="text-center p-3 bg-light rounded">
                    <div class="text-info fs-4">
                        {{ $revenue_data['total'] > 0 ? number_format(($revenue_breakdown['affiliate_commission'] / $revenue_data['total']) * 100, 1) : 0 }}%
                    </div>
                    <div class="small text-muted">Tỷ lệ hoa hồng</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Actions -->
    <div class="debug-card">
        <h5 class="mb-3">Hành động</h5>
        <div class="d-flex gap-3">
            <button class="btn btn-primary" onclick="window.location.reload()">
                <i class="fi-rr-refresh me-2"></i>Làm mới dữ liệu
            </button>
            <button class="btn btn-success" onclick="exportRevenue()">
                <i class="fi-rr-download me-2"></i>Xuất báo cáo
            </button>
            <a href="{{ route('admin.student.index') }}" class="btn btn-secondary">
                <i class="fi-rr-arrow-left me-2"></i>Quay lại CRM
            </a>
        </div>
    </div>
</div>
@endsection

@push('js')
<script>
function exportRevenue() {
    // Tạo CSV export
    const data = @json($revenue_breakdown);
    console.log('Exporting revenue data:', data);
    alert('Tính năng xuất báo cáo sẽ được phát triển trong phiên bản tiếp theo');
}
</script>
@endpush
