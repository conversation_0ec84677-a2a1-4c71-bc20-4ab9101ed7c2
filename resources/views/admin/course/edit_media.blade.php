<style>
.media-upload-container {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.upload-section {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 2px dashed #e9ecef;
    transition: all 0.3s ease;
    height: 100%;
}

.upload-section:hover {
    border-color: #007bff;
    background: #f8f9ff;
}

.upload-area {
    text-align: center;
    padding: 2rem;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.upload-area:hover {
    background: #f1f3f4;
}

.upload-icon {
    font-size: 3rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

.upload-text {
    color: #495057;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
}

.upload-hint {
    color: #6c757d;
    font-size: 0.9rem;
}

.preview-container {
    margin-top: 1rem;
    text-align: center;
}

.preview-image {
    max-width: 100%;
    max-height: 150px;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    margin-bottom: 1rem;
}

.current-image {
    border: 3px solid #28a745;
}

.change-btn {
    background: #007bff;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
}

.change-btn:hover {
    background: #0056b3;
}

.video-options {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    border: 1px solid #e9ecef;
}

.radio-group {
    display: flex;
    gap: 2rem;
    margin-bottom: 1rem;
}

.radio-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.radio-option input[type="radio"] {
    margin: 0;
}

.section-title {
    color: #495057;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.section-icon {
    color: #007bff;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .media-upload-container {
        padding: 1rem;
    }

    .upload-section {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .upload-area {
        padding: 1.5rem;
    }

    .section-title {
        font-size: 1rem;
    }
}
</style>

<div class="media-upload-container">
    <!-- Images Row -->
    <div class="row mb-4">
        <!-- Thumbnail Section -->
        <div class="col-md-6">
            <div class="upload-section">
                <h5 class="section-title">
                    <i class="fi-rr-picture section-icon"></i>
                    Hình ảnh đại diện (Thumbnail)
                </h5>

                <div class="upload-area" onclick="document.getElementById('thumbnail').click()">
                    <div id="thumbnail-upload-area">
                        @if($course_details->thumbnail && file_exists(public_path($course_details->thumbnail)))
                            <div class="preview-container">
                                <img src="{{ asset($course_details->thumbnail) }}" class="preview-image current-image" alt="Current thumbnail">
                                <div>
                                    <p class="text-success mb-2">
                                        <i class="fi-rr-check-circle"></i> Ảnh hiện tại
                                    </p>
                                    <button type="button" class="change-btn">Thay đổi ảnh</button>
                                </div>
                            </div>
                        @else
                            <div class="upload-icon">
                                <i class="fi-rr-cloud-upload"></i>
                            </div>
                            <div class="upload-text">Tải lên hình ảnh đại diện</div>
                            <div class="upload-hint">
                                Kéo thả hoặc click để chọn file<br>
                                <small>Định dạng: JPG, PNG, GIF (Tối đa 5MB)<br>Kích thước khuyến nghị: 400x300px</small>
                            </div>
                        @endif
                    </div>
                </div>
                <input type="file" name="thumbnail" id="thumbnail" accept="image/*" style="display: none;" />
            </div>
        </div>

        <!-- Banner Section -->
        <div class="col-md-6">
            <div class="upload-section">
                <h5 class="section-title">
                    <i class="fi-rr-banner section-icon"></i>
                    Ảnh bìa (Banner)
                </h5>

                <div class="upload-area" onclick="document.getElementById('banner').click()">
                    <div id="banner-upload-area">
                        @if($course_details->banner && file_exists(public_path($course_details->banner)))
                            <div class="preview-container">
                                <img src="{{ asset($course_details->banner) }}" class="preview-image current-image" alt="Current banner">
                                <div>
                                    <p class="text-success mb-2">
                                        <i class="fi-rr-check-circle"></i> Ảnh hiện tại
                                    </p>
                                    <button type="button" class="change-btn">Thay đổi ảnh</button>
                                </div>
                            </div>
                        @else
                            <div class="upload-icon">
                                <i class="fi-rr-cloud-upload"></i>
                            </div>
                            <div class="upload-text">Tải lên ảnh bìa</div>
                            <div class="upload-hint">
                                Kéo thả hoặc click để chọn file<br>
                                <small>Định dạng: JPG, PNG, GIF (Tối đa 5MB)<br>Kích thước khuyến nghị: 1400x400px</small>
                            </div>
                        @endif
                    </div>
                </div>
                <input type="file" name="banner" id="banner" accept="image/*" style="display: none;" />
            </div>
        </div>
    </div>

    <!-- Preview Video Section -->
    <div class="video-options">
        <h5 class="section-title">
            <i class="fi-rr-video-camera section-icon"></i>
            Video giới thiệu
        </h5>

        @php
            $preview_video_type = str_contains($course_details->preview, 'youtu') ? 'youtube' : '';
            $preview_video_type = str_contains($course_details->preview, 'vimeo') && $preview_video_type == '' ? 'vimeo' : '';
            $preview_video_type = str_contains($course_details->preview, 'http') && $preview_video_type == '' ? 'html5' : '';
        @endphp

        <div class="radio-group">
            <div class="radio-option">
                <input type="radio" onchange="toggleVideoInput()" class="form-check-input" value="link" name="preview_video_provider" id="preview_video_link" @if($preview_video_type != '') checked @endif>
                <label for="preview_video_link">Video Link</label>
            </div>
            <div class="radio-option">
                <input type="radio" onchange="toggleVideoInput()" class="form-check-input" value="file" name="preview_video_provider" id="preview_video_file" @if($preview_video_type == '') checked @endif>
                <label for="preview_video_file">Upload Video File</label>
            </div>
        </div>

        <div class="@if($preview_video_type == '') d-none @endif" id="preview_link_input">
            <label for="preview_link" class="form-label">Link video</label>
            <input type="text" name="preview_link" id="preview_link" class="form-control" value="{{ $course_details->preview }}" placeholder="https://www.youtube.com/watch?v=...">
            <small class="text-muted">Hỗ trợ: <b>Youtube</b>, <b>Vimeo</b> hoặc <b>HTML5</b></small>
        </div>

        <div class="@if($preview_video_type != '') d-none @endif" id="preview_file_input">
            <label for="preview" class="form-label">File video</label>
            <input type="file" name="preview" class="form-control" id="preview" accept="video/*" />
            <small class="text-muted">Định dạng hỗ trợ: <b>.mp4</b>, <b>.webm</b>, <b>.ogg</b></small>
        </div>
    </div>
</div>

<script>
function toggleVideoInput() {
    const linkInput = document.getElementById('preview_link_input');
    const fileInput = document.getElementById('preview_file_input');
    const linkRadio = document.getElementById('preview_video_link');

    if (linkRadio.checked) {
        linkInput.classList.remove('d-none');
        fileInput.classList.add('d-none');
    } else {
        linkInput.classList.add('d-none');
        fileInput.classList.remove('d-none');
    }
}

// Thumbnail preview
document.getElementById('thumbnail').addEventListener('change', function() {
    const file = this.files[0];
    const uploadArea = document.getElementById('thumbnail-upload-area');

    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            uploadArea.innerHTML = `
                <div class="preview-container">
                    <img src="${e.target.result}" class="preview-image" alt="Preview thumbnail">
                    <div>
                        <p class="text-success mb-2">
                            <i class="fi-rr-check-circle"></i> Đã chọn: ${file.name}
                        </p>
                        <button type="button" class="change-btn" onclick="clearThumbnail()">Chọn ảnh khác</button>
                    </div>
                </div>
            `;
        };
        reader.readAsDataURL(file);
    }
});

// Banner preview
document.getElementById('banner').addEventListener('change', function() {
    const file = this.files[0];
    const uploadArea = document.getElementById('banner-upload-area');

    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            uploadArea.innerHTML = `
                <div class="preview-container">
                    <img src="${e.target.result}" class="preview-image" alt="Preview banner">
                    <div>
                        <p class="text-success mb-2">
                            <i class="fi-rr-check-circle"></i> Đã chọn: ${file.name}
                        </p>
                        <button type="button" class="change-btn" onclick="clearBanner()">Chọn ảnh khác</button>
                    </div>
                </div>
            `;
        };
        reader.readAsDataURL(file);
    }
});

function clearThumbnail() {
    document.getElementById('thumbnail').value = '';
    document.getElementById('thumbnail-upload-area').innerHTML = `
        <div class="upload-icon">
            <i class="fi-rr-cloud-upload"></i>
        </div>
        <div class="upload-text">Tải lên hình ảnh đại diện</div>
        <div class="upload-hint">
            Kéo thả hoặc click để chọn file<br>
            <small>Định dạng: JPG, PNG, GIF (Tối đa 5MB) - Kích thước khuyến nghị: 400x300px</small>
        </div>
    `;
}

function clearBanner() {
    document.getElementById('banner').value = '';
    document.getElementById('banner-upload-area').innerHTML = `
        <div class="upload-icon">
            <i class="fi-rr-cloud-upload"></i>
        </div>
        <div class="upload-text">Tải lên ảnh bìa</div>
        <div class="upload-hint">
            Kéo thả hoặc click để chọn file<br>
            <small>Định dạng: JPG, PNG, GIF (Tối đa 5MB) - Kích thước khuyến nghị: 1400x400px</small>
        </div>
    `;
}
</script>
