
@php
    $seo_meta_tag = App\Models\SeoField::where('course_id', $course_details->id)->firstOrNew();
@endphp

<style>
.seo-section {
    background: #fff;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    margin-bottom: 20px;
}

.seo-section-title {
    font-size: 18px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.seo-section-desc {
    color: #718096;
    font-size: 14px;
    margin-bottom: 20px;
}

.modern-form-group {
    margin-bottom: 20px;
}

.modern-label {
    display: block;
    font-weight: 500;
    color: #374151;
    margin-bottom: 6px;
    font-size: 14px;
}

.modern-input, .modern-textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.2s ease;
    background: #fff;
}

.modern-input:focus, .modern-textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.help-text {
    font-size: 12px;
    color: #6b7280;
    margin-top: 4px;
}

.auto-fill-btn {
    background: #f3f4f6;
    border: 1px solid #d1d5db;
    color: #374151;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    margin-left: 8px;
    transition: all 0.2s ease;
}

.auto-fill-btn:hover {
    background: #e5e7eb;
}

.image-preview {
    border: 2px dashed #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    text-align: center;
    margin-bottom: 12px;
    background: #f9fafb;
}

.image-preview img {
    max-width: 200px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.advanced-toggle {
    color: #3b82f6;
    cursor: pointer;
    font-size: 14px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    margin-top: 16px;
}

.advanced-toggle:hover {
    text-decoration: underline;
}

.advanced-section {
    display: none;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #e5e7eb;
}
</style>

<!-- SEO Cơ bản -->
<div class="seo-section">
    <div class="seo-section-title">
        <i class="fi-rr-search"></i>
        Tối ưu hóa công cụ tìm kiếm (SEO)
    </div>
    <div class="seo-section-desc">
        Cải thiện khả năng hiển thị của khóa học trên Google và các công cụ tìm kiếm khác
    </div>

    <div class="modern-form-group">
        <label class="modern-label">
            Tiêu đề SEO <span class="text-danger">*</span>
            <button type="button" class="auto-fill-btn" onclick="autoFillMetaTitle()">Tự động điền</button>
        </label>
        <input type="text"
               class="modern-input"
               id="meta_title"
               name="meta_title"
               value="{{ $seo_meta_tag->meta_title }}"
               placeholder="Nhập tiêu đề tối ưu cho SEO (50-60 ký tự)"
               maxlength="60">
        <div class="help-text">
            Tiêu đề này sẽ hiển thị trên kết quả tìm kiếm Google. Nên từ 50-60 ký tự.
        </div>
    </div>

    <div class="modern-form-group">
        <label class="modern-label">
            Mô tả SEO <span class="text-danger">*</span>
            <button type="button" class="auto-fill-btn" onclick="autoFillMetaDescription()">Tự động điền</button>
        </label>
        <textarea class="modern-textarea"
                  id="meta_description"
                  name="meta_description"
                  rows="3"
                  placeholder="Mô tả ngắn gọn về khóa học (150-160 ký tự)"
                  maxlength="160">{{ $seo_meta_tag->meta_description }}</textarea>
        <div class="help-text">
            Mô tả này sẽ hiển thị dưới tiêu đề trên kết quả tìm kiếm. Nên từ 150-160 ký tự.
        </div>
    </div>

    <div class="modern-form-group">
        <label class="modern-label">Từ khóa SEO</label>
        <input type="text"
               name="meta_keywords"
               value="{{ $seo_meta_tag->meta_keywords }}"
               class="tagify modern-input"
               id="meta_keywords"
               placeholder="Nhập từ khóa và nhấn Enter">
        <div class="help-text">
            Nhập các từ khóa liên quan đến khóa học, cách nhau bằng dấu phẩy.
        </div>
    </div>
</div>

<!-- Social Media -->
<div class="seo-section">
    <div class="seo-section-title">
        <i class="fi-rr-share"></i>
        Tối ưu hóa mạng xã hội
    </div>
    <div class="seo-section-desc">
        Tùy chỉnh cách khóa học hiển thị khi được chia sẻ trên Facebook, Twitter, LinkedIn
    </div>

    <div class="modern-form-group">
        <label class="modern-label">
            Tiêu đề chia sẻ
            <button type="button" class="auto-fill-btn" onclick="autoFillOgTitle()">Tự động điền</button>
        </label>
        <input type="text"
               class="modern-input"
               id="og_title"
               name="og_title"
               value="{{ $seo_meta_tag->og_title }}"
               placeholder="Tiêu đề khi chia sẻ trên mạng xã hội">
        <div class="help-text">
            Tiêu đề hiển thị khi khóa học được chia sẻ trên Facebook, Twitter, LinkedIn.
        </div>
    </div>

    <div class="modern-form-group">
        <label class="modern-label">
            Mô tả chia sẻ
            <button type="button" class="auto-fill-btn" onclick="autoFillOgDescription()">Tự động điền</button>
        </label>
        <textarea class="modern-textarea"
                  id="og_description"
                  name="og_description"
                  rows="3"
                  placeholder="Mô tả khi chia sẻ trên mạng xã hội">{{ $seo_meta_tag->og_description }}</textarea>
        <div class="help-text">
            Mô tả hiển thị khi khóa học được chia sẻ trên mạng xã hội.
        </div>
    </div>

    <div class="modern-form-group">
        <label class="modern-label">Ảnh chia sẻ</label>
        <div class="image-preview">
            @if($seo_meta_tag->og_image)
                <img src="{{ get_image($seo_meta_tag->og_image) }}" alt="Ảnh chia sẻ">
            @else
                <div style="color: #9ca3af; padding: 20px;">
                    <i class="fi-rr-picture" style="font-size: 24px; margin-bottom: 8px;"></i>
                    <div>Chưa có ảnh chia sẻ</div>
                </div>
            @endif
        </div>
        <input type="file"
               class="modern-input"
               id="og_image"
               name="og_image"
               accept="image/*">
        <input type="hidden" name="old_og_image" value="{{ $seo_meta_tag->og_image }}">
        <div class="help-text">
            Ảnh hiển thị khi khóa học được chia sẻ. Kích thước khuyến nghị: 1200x630px.
        </div>
    </div>

    <a href="#" class="advanced-toggle" onclick="toggleAdvanced(event)">
        <i class="fi-rr-settings"></i>
        Cài đặt nâng cao
        <i class="fi-rr-angle-down" id="advanced-icon"></i>
    </a>

    <div class="advanced-section" id="advanced-section">
        <div class="modern-form-group">
            <label class="modern-label">Meta Robot</label>
            <input type="text"
                   class="modern-input"
                   id="meta_robot"
                   name="meta_robot"
                   value="{{ $seo_meta_tag->meta_robot ?: 'index, follow' }}"
                   placeholder="index, follow">
            <div class="help-text">
                Hướng dẫn cho robot tìm kiếm. Mặc định: "index, follow"
            </div>
        </div>

        <div class="modern-form-group">
            <label class="modern-label">Canonical URL</label>
            <input type="text"
                   class="modern-input"
                   id="canonical_url"
                   name="canonical_url"
                   value="{{ $seo_meta_tag->canonical_url ?: route('course.details', ['slug' => $course_details->slug]) }}"
                   placeholder="URL chính thức của khóa học">
            <div class="help-text">
                URL chính thức để tránh nội dung trùng lặp. Tự động điền từ URL khóa học.
            </div>
        </div>

        <div class="modern-form-group">
            <label class="modern-label">JSON-LD Schema</label>
            <textarea class="modern-textarea"
                      id="json_ld"
                      name="json_ld"
                      rows="4"
                      placeholder='{"@context": "https://schema.org", "@type": "Course", ...}'>{{ $seo_meta_tag->json_ld }}</textarea>
            <div class="help-text">
                Structured data cho Google Rich Results. Để trống nếu không rõ.
            </div>
        </div>
    </div>
</div>

<script>
function autoFillMetaTitle() {
    const courseTitle = "{{ $course_details->title }}";
    document.getElementById('meta_title').value = courseTitle + " - Khóa học online";
}

function autoFillMetaDescription() {
    const courseDescription = "{{ strip_tags($course_details->short_description) }}";
    const truncated = courseDescription.substring(0, 150) + (courseDescription.length > 150 ? '...' : '');
    document.getElementById('meta_description').value = truncated;
}

function autoFillOgTitle() {
    const courseTitle = "{{ $course_details->title }}";
    document.getElementById('og_title').value = courseTitle;
}

function autoFillOgDescription() {
    const courseDescription = "{{ strip_tags($course_details->short_description) }}";
    document.getElementById('og_description').value = courseDescription;
}

function toggleAdvanced(event) {
    event.preventDefault();
    const section = document.getElementById('advanced-section');
    const icon = document.getElementById('advanced-icon');

    if (section.style.display === 'none' || section.style.display === '') {
        section.style.display = 'block';
        icon.className = 'fi-rr-angle-up';
    } else {
        section.style.display = 'none';
        icon.className = 'fi-rr-angle-down';
    }
}

// Character counter for meta fields
document.getElementById('meta_title').addEventListener('input', function() {
    const length = this.value.length;
    const color = length > 60 ? 'red' : length > 50 ? 'orange' : 'green';
    this.style.borderColor = color;
});

document.getElementById('meta_description').addEventListener('input', function() {
    const length = this.value.length;
    const color = length > 160 ? 'red' : length > 150 ? 'orange' : 'green';
    this.style.borderColor = color;
});
</script>
