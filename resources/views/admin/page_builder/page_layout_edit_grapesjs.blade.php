<!DOCTYPE html>
<html lang="en">

<head>
    {{ config(['app.name' => get_settings('system_title')]) }}
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Chỉnh sửa trang với GrapesJS | {{ config('app.name') }}</title>
    <meta content="{{ csrf_token() }}" name="csrf_token" />

    <link rel="shortcut icon" href="{{ asset(get_frontend_settings('favicon')) }}" />

    <!-- GrapesJS CSS -->
    <link rel="stylesheet" href="https://unpkg.com/grapesjs/dist/css/grapes.min.css">
    <!-- GrapesJS Preset Webpage CSS -->
    <link rel="stylesheet" href="https://unpkg.com/grapesjs-preset-webpage/dist/grapesjs-preset-webpage.min.css">
    <!-- Custom GrapesJS CSS -->
    <link rel="stylesheet" href="{{ asset('assets/admin/css/grapesjs-custom.css') }}">
    <!-- Custom GrapesJS CSS -->
    <link rel="stylesheet" href="{{ asset('assets/admin/css/grapesjs-custom.css') }}">

    <style>
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .editor-top-bar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
            position: relative;
        }

        .editor-title {
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }

        .editor-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #4CAF50;
            color: white;
        }

        .btn-primary:hover {
            background: #45a049;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-1px);
        }

        .btn-info {
            background: #17a2b8;
            color: white;
        }

        .btn-info:hover {
            background: #138496;
            transform: translateY(-1px);
        }

        #gjs {
            height: calc(100vh - 70px);
            overflow: hidden;
        }

        /* Custom GrapesJS styling */
        .gjs-one-bg {
            background: #f8f9fa;
        }

        .gjs-two-color {
            color: #495057;
        }

        .gjs-three-bg {
            background: #e9ecef;
        }

        .gjs-four-color {
            color: #6c757d;
        }

        /* Loading overlay */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            font-size: 18px;
            color: #667eea;
        }

        .loading-overlay.hidden {
            display: none;
        }

        /* Success/Error messages */
        .alert {
            position: fixed;
            top: 80px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 6px;
            color: white;
            font-weight: 500;
            z-index: 1001;
            min-width: 300px;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        }

        .alert.show {
            opacity: 1;
            transform: translateX(0);
        }

        .alert-success {
            background: #28a745;
        }

        .alert-error {
            background: #dc3545;
        }
    </style>
</head>

<body>
    <!-- Loading overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div>Đang tải GrapesJS...</div>
    </div>

    <!-- Editor top bar -->
    <div class="editor-top-bar">
        <h1 class="editor-title">Chỉnh sửa trang với GrapesJS</h1>
        <div class="editor-actions">
            <button class="btn btn-info" onclick="previewPage()" title="Xem trước">
                <i class="fas fa-eye"></i> Xem trước
            </button>
            <button class="btn btn-primary" onclick="savePage()" title="Lưu trang">
                <i class="fas fa-save"></i> Lưu trang
            </button>
            <a class="btn btn-secondary" href="{{ route('admin.pages') }}" title="Quay lại">
                <i class="fas fa-arrow-left"></i> Quay lại
            </a>
        </div>
    </div>

    <!-- GrapesJS Editor -->
    <div id="gjs"></div>

    <!-- Alert container -->
    <div id="alertContainer"></div>

    <!-- GrapesJS Scripts -->
    <script src="https://unpkg.com/grapesjs"></script>
    <script src="https://unpkg.com/grapesjs-preset-webpage"></script>
    <script src="https://unpkg.com/grapesjs-blocks-basic"></script>
    <script src="https://unpkg.com/grapesjs-plugin-forms"></script>

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <script>
        let editor;
        const pageId = {{ $id }};

        // Initialize GrapesJS
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing GrapesJS...');
            console.log('GrapesJS available:', typeof grapesjs !== 'undefined');

            // Check if all required scripts are loaded
            setTimeout(function() {
                initializeEditor();
            }, 1000); // Wait 1 second for all scripts to load
        });

        function initializeEditor() {
            try {
                editor = grapesjs.init({
                    height: '100%',
                    container: '#gjs',
                    fromElement: false,
                    width: 'auto',
                    storageManager: false,

                    // Plugins
                    plugins: [
                        'gjs-preset-webpage',
                        'gjs-blocks-basic',
                        'gjs-plugin-forms'
                    ],

                pluginsOpts: {
                    'gjs-preset-webpage': {
                        modalImportTitle: 'Nhập mã HTML',
                        modalImportLabel: '<div style="margin-bottom: 10px; font-size: 13px;">Dán mã HTML/CSS của bạn và nhấn Nhập</div>',
                        modalImportContent: function(editor) {
                            return editor.getHtml() + '<style>' + editor.getCss() + '</style>';
                        },
                        filestackOpts: false,
                        aviaryOpts: false,
                        blocksBasicOpts: {
                            blocks: ['column1', 'column2', 'column3', 'text', 'link', 'image'],
                            flexGrid: 1,
                        }
                    }
                },

                canvas: {
                    styles: [
                        'https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.min.css'
                    ],
                    scripts: [
                        'https://code.jquery.com/jquery-3.3.1.slim.min.js',
                        'https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.min.js'
                    ],
                }
            });

            // Load existing content
            loadPageContent();

            // Hide loading overlay
            document.getElementById('loadingOverlay').classList.add('hidden');

            // Add custom blocks
            addCustomBlocks();

            console.log('GrapesJS initialized successfully');

            } catch (error) {
                console.error('Error initializing GrapesJS:', error);
                document.getElementById('loadingOverlay').innerHTML = '<div style="color: red;">Lỗi khởi tạo GrapesJS: ' + error.message + '</div>';
            }
        }

        function addCustomBlocks() {
            const blockManager = editor.BlockManager;

            // Hero Section Block
            blockManager.add('hero-section', {
                label: 'Hero Section',
                category: 'Sections',
                content: `
                    <section class="hero-section" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 100px 0; text-align: center;">
                        <div class="container">
                            <h1 style="font-size: 3rem; margin-bottom: 20px;">Tiêu đề chính</h1>
                            <p style="font-size: 1.2rem; margin-bottom: 30px;">Mô tả ngắn gọn về sản phẩm hoặc dịch vụ của bạn</p>
                            <a href="#" class="btn btn-primary btn-lg">Bắt đầu ngay</a>
                        </div>
                    </section>
                `,
                attributes: { class: 'fa fa-star' }
            });

            // Course Card Block
            blockManager.add('course-card', {
                label: 'Thẻ khóa học',
                category: 'Course',
                content: `
                    <div class="course-card" style="border: 1px solid #ddd; border-radius: 8px; overflow: hidden; margin: 20px 0;">
                        <img src="https://via.placeholder.com/300x200" alt="Course Image" style="width: 100%; height: 200px; object-fit: cover;">
                        <div style="padding: 20px;">
                            <h3 style="margin: 0 0 10px 0;">Tên khóa học</h3>
                            <p style="color: #666; margin: 0 0 15px 0;">Mô tả ngắn về khóa học</p>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span style="font-size: 1.2rem; font-weight: bold; color: #e74c3c;">500,000 VNĐ</span>
                                <a href="#" class="btn btn-primary">Đăng ký</a>
                            </div>
                        </div>
                    </div>
                `,
                attributes: { class: 'fa fa-graduation-cap' }
            });

            // Testimonial Block
            blockManager.add('testimonial', {
                label: 'Đánh giá',
                category: 'Content',
                content: `
                    <div class="testimonial" style="background: #f8f9fa; padding: 30px; border-radius: 8px; text-align: center; margin: 20px 0;">
                        <img src="https://via.placeholder.com/80x80" alt="Avatar" style="width: 80px; height: 80px; border-radius: 50%; margin-bottom: 20px;">
                        <blockquote style="font-style: italic; font-size: 1.1rem; margin: 0 0 20px 0;">"Đây là một khóa học tuyệt vời, tôi đã học được rất nhiều điều bổ ích."</blockquote>
                        <cite style="font-weight: bold;">Nguyễn Văn A</cite>
                        <div style="color: #ffc107; margin-top: 10px;">★★★★★</div>
                    </div>
                `,
                attributes: { class: 'fa fa-quote-left' }
            });

            // Contact Form Block
            blockManager.add('contact-form', {
                label: 'Form liên hệ',
                category: 'Forms',
                content: `
                    <form class="contact-form" style="max-width: 600px; margin: 0 auto; padding: 30px; background: #f8f9fa; border-radius: 8px;">
                        <h3 style="text-align: center; margin-bottom: 30px;">Liên hệ với chúng tôi</h3>
                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">Họ và tên</label>
                            <input type="text" name="name" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">Email</label>
                            <input type="email" name="email" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">Tin nhắn</label>
                            <textarea name="message" rows="5" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; resize: vertical;"></textarea>
                        </div>
                        <button type="submit" style="background: #007bff; color: white; padding: 12px 30px; border: none; border-radius: 4px; cursor: pointer; width: 100%;">Gửi tin nhắn</button>
                    </form>
                `,
                attributes: { class: 'fa fa-envelope' }
            });
        }

        function loadPageContent() {
            fetch(`{{ route('admin.page.grapesjs.load', $id) }}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        if (data.grapesjs_components) {
                            editor.loadProjectData(data.grapesjs_components);
                        } else if (data.grapesjs_html) {
                            editor.setComponents(data.grapesjs_html);
                            editor.setStyle(data.grapesjs_css || '');
                        }
                    }
                })
                .catch(error => {
                    console.error('Error loading page content:', error);
                    showAlert('Có lỗi khi tải nội dung trang', 'error');
                });
        }

        function savePage() {
            const loadingOverlay = document.getElementById('loadingOverlay');
            loadingOverlay.classList.remove('hidden');
            loadingOverlay.innerHTML = '<div>Đang lưu trang...</div>';

            const html = editor.getHtml();
            const css = editor.getCss();
            const components = editor.getProjectData();

            fetch(`{{ route('admin.page.grapesjs.save', $id) }}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf_token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    grapesjs_html: html,
                    grapesjs_css: css,
                    grapesjs_components: components
                })
            })
            .then(response => response.json())
            .then(data => {
                loadingOverlay.classList.add('hidden');
                if (data.success) {
                    showAlert('Trang đã được lưu thành công!', 'success');
                } else {
                    showAlert(data.message || 'Có lỗi khi lưu trang', 'error');
                }
            })
            .catch(error => {
                loadingOverlay.classList.add('hidden');
                console.error('Error saving page:', error);
                showAlert('Có lỗi khi lưu trang', 'error');
            });
        }

        function previewPage() {
            const html = editor.getHtml();
            const css = editor.getCss();

            const previewWindow = window.open('', '_blank');
            previewWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>Xem trước trang</title>
                    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.min.css" rel="stylesheet">
                    <style>${css}</style>
                </head>
                <body>
                    ${html}
                    <script src="https://code.jquery.com/jquery-3.3.1.slim.min.js"></script>
                    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.min.js"></script>
                </body>
                </html>
            `);
            previewWindow.document.close();
        }

        function showAlert(message, type) {
            const alertContainer = document.getElementById('alertContainer');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;

            alertContainer.appendChild(alert);

            // Show alert
            setTimeout(() => {
                alert.classList.add('show');
            }, 100);

            // Hide alert after 3 seconds
            setTimeout(() => {
                alert.classList.remove('show');
                setTimeout(() => {
                    alertContainer.removeChild(alert);
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>
