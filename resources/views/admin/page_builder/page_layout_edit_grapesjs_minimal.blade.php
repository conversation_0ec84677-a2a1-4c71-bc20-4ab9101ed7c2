<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>GrapesJS Editor - Minimal</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <link rel="stylesheet" href="https://unpkg.com/grapesjs@0.19.5/dist/css/grapes.min.css">
    <style>
        body {
            margin: 0;
            font-family: Arial, sans-serif;
        }
        
        .header {
            background: #333;
            color: white;
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .btn {
            padding: 8px 16px;
            margin: 0 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            text-decoration: none;
            color: white;
        }
        
        .btn-save { background: #28a745; }
        .btn-preview { background: #007bff; }
        .btn-back { background: #6c757d; }
        
        #gjs {
            height: calc(100vh - 60px);
            border: 0;
        }
        
        .status {
            position: fixed;
            top: 70px;
            right: 20px;
            padding: 10px 15px;
            background: #333;
            color: white;
            border-radius: 3px;
            z-index: 1000;
            display: none;
        }
        
        .status.show { display: block; }
        .status.success { background: #28a745; }
        .status.error { background: #dc3545; }
    </style>
</head>
<body>
    <div class="header">
        <h3>GrapesJS Editor - Trang {{ $id }}</h3>
        <div>
            <button class="btn btn-save" onclick="save()">Lưu</button>
            <button class="btn btn-preview" onclick="preview()">Xem trước</button>
            <a href="{{ route('admin.pages') }}" class="btn btn-back">Quay lại</a>
        </div>
    </div>
    
    <div id="gjs">
        <div>Đang tải editor...</div>
    </div>
    
    <div id="status" class="status"></div>

    <script src="https://unpkg.com/grapesjs@0.19.5/dist/grapes.min.js"></script>
    <script>
        let editor;
        const pageId = {{ $id }};
        
        // Initialize when page loads
        window.addEventListener('load', function() {
            console.log('Page loaded, initializing GrapesJS...');
            
            try {
                editor = grapesjs.init({
                    container: '#gjs',
                    height: '100%',
                    width: 'auto',
                    storageManager: false,
                    blockManager: {
                        appendTo: '#gjs',
                        blocks: [
                            {
                                id: 'section',
                                label: '<b>Section</b>',
                                attributes: { class: 'gjs-block-section' },
                                content: '<section><h1>This is a simple title</h1><div>This is just a Lorem text: Lorem ipsum dolor sit amet</div></section>',
                            },
                            {
                                id: 'text',
                                label: 'Text',
                                content: '<div data-gjs-type="text">Insert your text here</div>',
                            },
                            {
                                id: 'image',
                                label: 'Image',
                                select: true,
                                content: { type: 'image' },
                                activate: true,
                            }
                        ]
                    },
                    layerManager: {
                        appendTo: '.layers-container'
                    },
                    styleManager: {
                        appendTo: '.styles-container',
                        sectors: [{
                            name: 'Dimension',
                            open: false,
                            buildProps: ['width', 'min-height', 'padding'],
                            properties: [{
                                type: 'integer',
                                name: 'The width',
                                property: 'width',
                                units: ['px', '%'],
                                defaults: 'auto',
                                min: 0,
                            }]
                        },{
                            name: 'Extra',
                            open: false,
                            buildProps: ['background-color', 'box-shadow', 'custom-prop'],
                            properties: [{
                                id: 'custom-prop',
                                name: 'Custom Label',
                                property: 'font-size',
                                type: 'select',
                                defaults: '32px',
                                options: [
                                    { value: '12px', name: 'Tiny' },
                                    { value: '18px', name: 'Medium' },
                                    { value: '32px', name: 'Big' },
                                ]
                            }]
                        }]
                    },
                    traitManager: {
                        appendTo: '.traits-container',
                    },
                    selectorManager: {
                        appendTo: '.styles-container'
                    },
                    panels: {
                        defaults: [{
                            id: 'layers',
                            el: '.panel__right',
                            resizable: {
                                maxDim: 350,
                                minDim: 200,
                                tc: 0,
                                cl: 1,
                                cr: 0,
                                bc: 0,
                                keyWidth: 'flex-basis',
                            },
                        }, {
                            id: 'panel-switcher',
                            el: '.panel__switcher',
                            buttons: [{
                                id: 'show-layers',
                                active: true,
                                label: 'Layers',
                                command: 'show-layers',
                                togglable: false,
                            }, {
                                id: 'show-style',
                                active: true,
                                label: 'Styles',
                                command: 'show-styles',
                                togglable: false,
                            }],
                        }]
                    },
                    deviceManager: {
                        devices: [{
                            name: 'Desktop',
                            width: '',
                        }, {
                            name: 'Mobile',
                            width: '320px',
                            widthMedia: '480px',
                        }]
                    },
                });
                
                // Load existing content
                loadContent();
                
                showStatus('GrapesJS đã khởi tạo thành công!', 'success');
                console.log('GrapesJS initialized successfully');
                
            } catch (error) {
                console.error('Error:', error);
                showStatus('Lỗi khởi tạo: ' + error.message, 'error');
            }
        });
        
        function loadContent() {
            fetch(`{{ route('admin.page.grapesjs.load', $id) }}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.grapesjs_html) {
                        editor.setComponents(data.grapesjs_html);
                        if (data.grapesjs_css) {
                            editor.setStyle(data.grapesjs_css);
                        }
                        console.log('Content loaded');
                    }
                })
                .catch(error => {
                    console.error('Load error:', error);
                    showStatus('Lỗi tải nội dung', 'error');
                });
        }
        
        function save() {
            if (!editor) {
                showStatus('Editor chưa sẵn sàng', 'error');
                return;
            }
            
            showStatus('Đang lưu...', '');
            
            const html = editor.getHtml();
            const css = editor.getCss();
            
            fetch(`{{ route('admin.page.grapesjs.save', $id) }}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    grapesjs_html: html,
                    grapesjs_css: css,
                    grapesjs_components: editor.getProjectData()
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showStatus('Đã lưu thành công!', 'success');
                } else {
                    showStatus('Lỗi: ' + (data.message || 'Không thể lưu'), 'error');
                }
            })
            .catch(error => {
                console.error('Save error:', error);
                showStatus('Lỗi khi lưu', 'error');
            });
        }
        
        function preview() {
            if (!editor) {
                showStatus('Editor chưa sẵn sàng', 'error');
                return;
            }
            
            const html = editor.getHtml();
            const css = editor.getCss();
            
            const win = window.open('', '_blank');
            win.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>Preview</title>
                    <style>${css}</style>
                </head>
                <body>${html}</body>
                </html>
            `);
            win.document.close();
        }
        
        function showStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = 'status show ' + type;
            
            setTimeout(() => {
                status.classList.remove('show');
            }, 3000);
        }
    </script>
</body>
</html>
