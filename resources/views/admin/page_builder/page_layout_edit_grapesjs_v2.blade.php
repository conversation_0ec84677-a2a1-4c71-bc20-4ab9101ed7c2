<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GrapesJS Editor</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- GrapesJS CSS -->
    <link rel="stylesheet" href="https://unpkg.com/grapesjs/dist/css/grapes.min.css">

    <style>
        * {
            box-sizing: border-box;
        }

        body, html {
            margin: 0;
            padding: 0;
            height: 100vh;
            font-family: Arial, sans-serif;
        }

        .editor-header {
            background: #2c3e50;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .editor-title {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }

        .editor-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .editor-row {
            display: flex;
            height: calc(100vh - 70px);
        }

        .editor-canvas {
            flex: 1;
            position: relative;
        }

        .panel__top {
            background: #444;
            color: white;
            padding: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .panel__basic-actions,
        .panel__devices {
            display: flex;
            gap: 5px;
        }

        .panel__switcher {
            display: flex;
            gap: 5px;
        }

        .panel__right {
            width: 300px;
            background: #f8f9fa;
            border-left: 1px solid #ddd;
            display: flex;
            flex-direction: column;
        }

        .layers-container,
        .styles-container,
        .traits-container {
            flex: 1;
            padding: 10px;
            overflow-y: auto;
        }

        #gjs {
            height: 100%;
            border: none;
        }

        .gjs-pn-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 3px;
            cursor: pointer;
            margin: 0 2px;
        }

        .gjs-pn-btn:hover {
            background: #2980b9;
        }

        .gjs-pn-btn.gjs-pn-active {
            background: #27ae60;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            font-size: 16px;
        }

        .loading-overlay.hidden {
            display: none;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            position: fixed;
            top: 80px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 4px;
            color: white;
            font-weight: 500;
            z-index: 10001;
            min-width: 300px;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        }

        .alert.show {
            opacity: 1;
            transform: translateX(0);
        }

        .alert-success {
            background: #27ae60;
        }

        .alert-error {
            background: #e74c3c;
        }

        /* GrapesJS Custom Styling */
        .gjs-one-bg {
            background-color: #f8f9fa;
        }

        .gjs-two-color {
            color: #495057;
        }

        .gjs-three-bg {
            background-color: #e9ecef;
        }

        .gjs-four-color {
            color: #6c757d;
        }

        .gjs-block {
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .gjs-block:hover {
            transform: scale(1.05);
        }

        .gjs-toolbar {
            background: white;
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="spinner"></div>
        <div>Đang khởi tạo GrapesJS...</div>
    </div>

    <!-- Editor Header -->
    <div class="editor-header">
        <h1 class="editor-title">GrapesJS Editor - Trang {{ $id }}</h1>
        <div class="editor-actions">
            <button class="btn btn-primary" onclick="previewPage()" title="Xem trước">
                Xem trước
            </button>
            <button class="btn btn-success" onclick="savePage()" title="Lưu trang">
                Lưu trang
            </button>
            <a class="btn btn-secondary" href="{{ route('admin.pages') }}" title="Quay lại">
                Quay lại
            </a>
        </div>
    </div>

    <!-- GrapesJS Container -->
    <div class="editor-row">
        <div class="editor-canvas">
            <div class="panel__top">
                <div class="panel__basic-actions"></div>
                <div class="panel__devices"></div>
            </div>
            <div id="gjs"></div>
        </div>
        <div class="panel__right">
            <div class="panel__switcher"></div>
            <div class="layers-container"></div>
            <div class="styles-container"></div>
            <div class="traits-container"></div>
        </div>
    </div>

    <!-- Alert Container -->
    <div id="alertContainer"></div>

    <!-- GrapesJS JavaScript -->
    <script src="https://unpkg.com/grapesjs@0.20.4/dist/grapes.min.js"></script>

    <script>
        let editor = null;
        const pageId = {{ $id }};

        // Wait for DOM to be fully loaded
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, starting GrapesJS initialization...');

            // Add a small delay to ensure all resources are loaded
            setTimeout(initializeGrapesJS, 500);
        });

        function initializeGrapesJS() {
            try {
                console.log('Initializing GrapesJS...');

                // Check if GrapesJS is available
                if (typeof grapesjs === 'undefined') {
                    throw new Error('GrapesJS library not loaded');
                }

                // Initialize GrapesJS with minimal configuration
                editor = grapesjs.init({
                    container: '#gjs',
                    height: '100%',
                    width: 'auto',
                    storageManager: false,

                    // Panels configuration
                    panels: {
                        defaults: [
                            {
                                id: 'panel-top',
                                el: '.panel__top',
                            },
                            {
                                id: 'basic-views',
                                el: '.panel__basic-actions',
                                buttons: [
                                    {
                                        id: 'visibility',
                                        active: true,
                                        className: 'btn-toggle-borders',
                                        label: '<i class="fa fa-clone"></i>',
                                        command: 'sw-visibility',
                                    }
                                ]
                            },
                            {
                                id: 'panel-devices',
                                el: '.panel__devices',
                                buttons: [
                                    {
                                        id: 'device-desktop',
                                        label: '<i class="fa fa-desktop"></i>',
                                        command: 'set-device-desktop',
                                        active: true,
                                        togglable: false,
                                    },
                                    {
                                        id: 'device-tablet',
                                        label: '<i class="fa fa-tablet"></i>',
                                        command: 'set-device-tablet',
                                        togglable: false,
                                    },
                                    {
                                        id: 'device-mobile',
                                        label: '<i class="fa fa-mobile"></i>',
                                        command: 'set-device-mobile',
                                        togglable: false,
                                    }
                                ]
                            },
                            {
                                id: 'panel-switcher',
                                el: '.panel__switcher',
                                buttons: [
                                    {
                                        id: 'show-layers',
                                        active: true,
                                        label: 'Layers',
                                        command: 'show-layers',
                                        togglable: false,
                                    },
                                    {
                                        id: 'show-style',
                                        active: true,
                                        label: 'Styles',
                                        command: 'show-styles',
                                        togglable: false,
                                    },
                                    {
                                        id: 'show-traits',
                                        active: true,
                                        label: 'Traits',
                                        command: 'show-traits',
                                        togglable: false,
                                    }
                                ]
                            }
                        ]
                    },

                    // Device manager for responsive design
                    deviceManager: {
                        devices: [
                            {
                                name: 'Desktop',
                                width: '',
                            },
                            {
                                name: 'Tablet',
                                width: '768px',
                                widthMedia: '992px',
                            },
                            {
                                name: 'Mobile',
                                width: '320px',
                                widthMedia: '768px',
                            }
                        ]
                    },

                    // Managers configuration
                    blockManager: {
                        appendTo: '.editor-canvas'
                    },

                    layerManager: {
                        appendTo: '.layers-container'
                    },

                    styleManager: {
                        appendTo: '.styles-container',
                        sectors: [
                            {
                                name: 'General',
                                open: false,
                                buildProps: ['float', 'display', 'position', 'top', 'right', 'left', 'bottom']
                            },
                            {
                                name: 'Dimension',
                                open: false,
                                buildProps: ['width', 'height', 'max-width', 'min-height', 'margin', 'padding']
                            },
                            {
                                name: 'Typography',
                                open: false,
                                buildProps: ['font-family', 'font-size', 'font-weight', 'letter-spacing', 'color', 'line-height', 'text-align', 'text-decoration', 'text-shadow']
                            },
                            {
                                name: 'Decorations',
                                open: false,
                                buildProps: ['opacity', 'background-color', 'border-radius', 'border', 'box-shadow', 'background']
                            }
                        ]
                    },

                    traitManager: {
                        appendTo: '.traits-container'
                    },

                    selectorManager: {
                        appendTo: '.styles-container'
                    },

                    // Canvas configuration
                    canvas: {
                        styles: [
                            'https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css'
                        ]
                    }
                });

                // Add commands for device switching
                editor.Commands.add('set-device-desktop', {
                    run: function(editor) {
                        editor.setDevice('Desktop');
                    }
                });

                editor.Commands.add('set-device-tablet', {
                    run: function(editor) {
                        editor.setDevice('Tablet');
                    }
                });

                editor.Commands.add('set-device-mobile', {
                    run: function(editor) {
                        editor.setDevice('Mobile');
                    }
                });

                // Add commands for panel switching
                editor.Commands.add('show-layers', {
                    run: function(editor) {
                        const lm = editor.LayerManager;
                        lm.render();
                    }
                });

                editor.Commands.add('show-styles', {
                    run: function(editor) {
                        const sm = editor.StyleManager;
                        sm.render();
                    }
                });

                editor.Commands.add('show-traits', {
                    run: function(editor) {
                        const tm = editor.TraitManager;
                        tm.render();
                    }
                });

                // Add basic blocks
                addBasicBlocks();

                // Load existing content
                loadPageContent();

                // Hide loading overlay
                hideLoading();

                console.log('GrapesJS initialized successfully');
                showAlert('GrapesJS đã được khởi tạo thành công!', 'success');

            } catch (error) {
                console.error('Error initializing GrapesJS:', error);
                showError('Lỗi khởi tạo GrapesJS: ' + error.message);
            }
        }

        function addBasicBlocks() {
            const blockManager = editor.BlockManager;

            // Basic blocks
            blockManager.add('text', {
                label: 'Văn bản',
                content: '<div data-gjs-type="text">Nhập văn bản của bạn ở đây</div>',
                category: 'Cơ bản',
                attributes: { class: 'fa fa-font' }
            });

            blockManager.add('image', {
                label: 'Hình ảnh',
                content: '<img src="https://via.placeholder.com/350x250/78c5d6/fff/000" alt="Hình ảnh"/>',
                category: 'Cơ bản',
                attributes: { class: 'fa fa-image' }
            });

            blockManager.add('button', {
                label: 'Nút bấm',
                content: '<a class="btn btn-primary" href="#">Nút bấm</a>',
                category: 'Cơ bản',
                attributes: { class: 'fa fa-hand-pointer' }
            });

            blockManager.add('link', {
                label: 'Liên kết',
                content: '<a href="#">Liên kết</a>',
                category: 'Cơ bản',
                attributes: { class: 'fa fa-link' }
            });

            // Layout blocks
            blockManager.add('container', {
                label: 'Container',
                content: '<div class="container"><div class="row"><div class="col-12">Nội dung container</div></div></div>',
                category: 'Layout',
                attributes: { class: 'fa fa-square' }
            });

            blockManager.add('row', {
                label: 'Hàng',
                content: '<div class="row"><div class="col-6">Cột 1</div><div class="col-6">Cột 2</div></div>',
                category: 'Layout',
                attributes: { class: 'fa fa-columns' }
            });

            blockManager.add('column', {
                label: 'Cột',
                content: '<div class="col-12">Nội dung cột</div>',
                category: 'Layout',
                attributes: { class: 'fa fa-square-o' }
            });

            // Course blocks
            blockManager.add('hero-section', {
                label: 'Hero Section',
                content: `
                    <section class="hero-section" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 100px 0; text-align: center;">
                        <div class="container">
                            <h1 style="font-size: 3rem; margin-bottom: 20px;">Tiêu đề chính</h1>
                            <p style="font-size: 1.2rem; margin-bottom: 30px;">Mô tả ngắn gọn về sản phẩm hoặc dịch vụ của bạn</p>
                            <a href="#" class="btn btn-primary btn-lg">Bắt đầu ngay</a>
                        </div>
                    </section>
                `,
                category: 'Khóa học',
                attributes: { class: 'fa fa-star' }
            });

            blockManager.add('course-card', {
                label: 'Thẻ khóa học',
                content: `
                    <div class="course-card" style="border: 1px solid #ddd; border-radius: 8px; overflow: hidden; margin: 20px 0;">
                        <img src="https://via.placeholder.com/300x200" alt="Course Image" style="width: 100%; height: 200px; object-fit: cover;">
                        <div style="padding: 20px;">
                            <h3 style="margin: 0 0 10px 0;">Tên khóa học</h3>
                            <p style="color: #666; margin: 0 0 15px 0;">Mô tả ngắn về khóa học</p>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span style="font-size: 1.2rem; font-weight: bold; color: #e74c3c;">500,000 VNĐ</span>
                                <a href="#" class="btn btn-primary">Đăng ký</a>
                            </div>
                        </div>
                    </div>
                `,
                category: 'Khóa học',
                attributes: { class: 'fa fa-graduation-cap' }
            });

            blockManager.add('testimonial', {
                label: 'Đánh giá',
                content: `
                    <div class="testimonial" style="background: #f8f9fa; padding: 30px; border-radius: 8px; text-align: center; margin: 20px 0;">
                        <img src="https://via.placeholder.com/80x80" alt="Avatar" style="width: 80px; height: 80px; border-radius: 50%; margin-bottom: 20px;">
                        <blockquote style="font-style: italic; font-size: 1.1rem; margin: 0 0 20px 0;">"Đây là một khóa học tuyệt vời, tôi đã học được rất nhiều điều bổ ích."</blockquote>
                        <cite style="font-weight: bold;">Nguyễn Văn A</cite>
                        <div style="color: #ffc107; margin-top: 10px;">★★★★★</div>
                    </div>
                `,
                category: 'Khóa học',
                attributes: { class: 'fa fa-quote-left' }
            });

            // Form blocks
            blockManager.add('contact-form', {
                label: 'Form liên hệ',
                content: `
                    <form class="contact-form" style="max-width: 600px; margin: 0 auto; padding: 30px; background: #f8f9fa; border-radius: 8px;">
                        <h3 style="text-align: center; margin-bottom: 30px;">Liên hệ với chúng tôi</h3>
                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">Họ và tên</label>
                            <input type="text" name="name" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">Email</label>
                            <input type="email" name="email" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">Tin nhắn</label>
                            <textarea name="message" rows="5" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; resize: vertical;"></textarea>
                        </div>
                        <button type="submit" style="background: #007bff; color: white; padding: 12px 30px; border: none; border-radius: 4px; cursor: pointer; width: 100%;">Gửi tin nhắn</button>
                    </form>
                `,
                category: 'Form',
                attributes: { class: 'fa fa-envelope' }
            });
        }

        function loadPageContent() {
            fetch(`{{ route('admin.page.grapesjs.load', $id) }}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        if (data.grapesjs_html) {
                            editor.setComponents(data.grapesjs_html);
                        }
                        if (data.grapesjs_css) {
                            editor.setStyle(data.grapesjs_css);
                        }
                        console.log('Content loaded successfully');
                    }
                })
                .catch(error => {
                    console.error('Error loading content:', error);
                    showAlert('Có lỗi khi tải nội dung trang', 'error');
                });
        }

        function savePage() {
            if (!editor) {
                showAlert('Editor chưa được khởi tạo', 'error');
                return;
            }

            showLoading('Đang lưu trang...');

            const html = editor.getHtml();
            const css = editor.getCss();
            const components = editor.getProjectData();

            fetch(`{{ route('admin.page.grapesjs.save', $id) }}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    grapesjs_html: html,
                    grapesjs_css: css,
                    grapesjs_components: components
                })
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                if (data.success) {
                    showAlert('Trang đã được lưu thành công!', 'success');
                } else {
                    showAlert(data.message || 'Có lỗi khi lưu trang', 'error');
                }
            })
            .catch(error => {
                hideLoading();
                console.error('Error saving page:', error);
                showAlert('Có lỗi khi lưu trang', 'error');
            });
        }

        function previewPage() {
            if (!editor) {
                showAlert('Editor chưa được khởi tạo', 'error');
                return;
            }

            const html = editor.getHtml();
            const css = editor.getCss();

            const previewWindow = window.open('', '_blank');
            previewWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>Xem trước trang</title>
                    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
                    <style>${css}</style>
                </head>
                <body>
                    ${html}
                </body>
                </html>
            `);
            previewWindow.document.close();
        }

        function showLoading(message = 'Đang tải...') {
            const overlay = document.getElementById('loadingOverlay');
            overlay.querySelector('div:last-child').textContent = message;
            overlay.classList.remove('hidden');
        }

        function hideLoading() {
            document.getElementById('loadingOverlay').classList.add('hidden');
        }

        function showError(message) {
            const overlay = document.getElementById('loadingOverlay');
            overlay.innerHTML = `<div style="color: #e74c3c; text-align: center;"><h3>Lỗi</h3><p>${message}</p><button class="btn btn-secondary" onclick="location.reload()">Tải lại trang</button></div>`;
        }

        function showAlert(message, type) {
            const alertContainer = document.getElementById('alertContainer');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;

            alertContainer.appendChild(alert);

            // Show alert
            setTimeout(() => {
                alert.classList.add('show');
            }, 100);

            // Hide alert after 3 seconds
            setTimeout(() => {
                alert.classList.remove('show');
                setTimeout(() => {
                    if (alertContainer.contains(alert)) {
                        alertContainer.removeChild(alert);
                    }
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>
