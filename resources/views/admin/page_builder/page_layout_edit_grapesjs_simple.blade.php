<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GrapesJS Editor - Simple</title>
    <meta content="{{ csrf_token() }}" name="csrf_token" />
    
    <!-- GrapesJS CSS -->
    <link rel="stylesheet" href="https://unpkg.com/grapesjs/dist/css/grapes.min.css">
    
    <style>
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
        }
        
        .editor-header {
            background: #333;
            color: white;
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .btn {
            padding: 8px 16px;
            margin: 0 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        #gjs {
            height: calc(100vh - 60px);
        }
        
        .loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255,255,255,0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            font-size: 18px;
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="loading" id="loading">Đang tải GrapesJS...</div>
    
    <div class="editor-header">
        <h3>GrapesJS Editor - Trang {{ $id }}</h3>
        <div>
            <button class="btn btn-primary" onclick="savePage()">Lưu</button>
            <button class="btn btn-secondary" onclick="previewPage()">Xem trước</button>
            <a href="{{ route('admin.pages') }}" class="btn btn-secondary">Quay lại</a>
        </div>
    </div>
    
    <div id="gjs"></div>

    <!-- GrapesJS JS -->
    <script src="https://unpkg.com/grapesjs"></script>
    
    <script>
        let editor;
        const pageId = {{ $id }};
        
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Initializing simple GrapesJS...');
            
            try {
                editor = grapesjs.init({
                    container: '#gjs',
                    height: '100%',
                    width: 'auto',
                    storageManager: false,
                    blockManager: {
                        appendTo: '#gjs'
                    },
                    styleManager: {
                        appendTo: '#gjs'
                    },
                    layerManager: {
                        appendTo: '#gjs'
                    },
                    traitManager: {
                        appendTo: '#gjs'
                    },
                    selectorManager: {
                        appendTo: '#gjs'
                    },
                    panels: {
                        defaults: [
                            {
                                id: 'layers',
                                el: '.panel__right',
                                resizable: {
                                    maxDim: 350,
                                    minDim: 200,
                                    tc: 0,
                                    cl: 1,
                                    cr: 0,
                                    bc: 0,
                                    keyWidth: 'flex-basis',
                                }
                            },
                            {
                                id: 'panel-switcher',
                                el: '.panel__switcher',
                                buttons: [
                                    {
                                        id: 'show-layers',
                                        active: true,
                                        label: 'Layers',
                                        command: 'show-layers',
                                        togglable: false,
                                    },
                                    {
                                        id: 'show-style',
                                        active: true,
                                        label: 'Styles',
                                        command: 'show-styles',
                                        togglable: false,
                                    }
                                ],
                            }
                        ]
                    },
                    deviceManager: {
                        devices: [
                            {
                                name: 'Desktop',
                                width: '',
                            },
                            {
                                name: 'Mobile',
                                width: '320px',
                                widthMedia: '480px',
                            }
                        ]
                    }
                });

                // Add some basic blocks
                editor.BlockManager.add('text-block', {
                    label: 'Text',
                    content: '<div data-gjs-type="text">Insert your text here</div>',
                    category: 'Basic'
                });

                editor.BlockManager.add('image-block', {
                    label: 'Image',
                    content: '<img src="https://via.placeholder.com/350x250/78c5d6/fff" alt="Image"/>',
                    category: 'Basic'
                });

                editor.BlockManager.add('button-block', {
                    label: 'Button',
                    content: '<a class="btn btn-primary" href="#">Button</a>',
                    category: 'Basic'
                });

                // Load existing content
                loadPageContent();
                
                document.getElementById('loading').classList.add('hidden');
                console.log('GrapesJS initialized successfully');
                
            } catch (error) {
                console.error('Error initializing GrapesJS:', error);
                document.getElementById('loading').innerHTML = 'Lỗi: ' + error.message;
            }
        });

        function loadPageContent() {
            fetch(`{{ route('admin.page.grapesjs.load', $id) }}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.grapesjs_html) {
                        editor.setComponents(data.grapesjs_html);
                        if (data.grapesjs_css) {
                            editor.setStyle(data.grapesjs_css);
                        }
                    }
                })
                .catch(error => {
                    console.error('Error loading content:', error);
                });
        }

        function savePage() {
            if (!editor) {
                alert('Editor chưa được khởi tạo');
                return;
            }

            const html = editor.getHtml();
            const css = editor.getCss();

            fetch(`{{ route('admin.page.grapesjs.save', $id) }}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf_token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    grapesjs_html: html,
                    grapesjs_css: css,
                    grapesjs_components: editor.getProjectData()
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Đã lưu thành công!');
                } else {
                    alert('Lỗi: ' + (data.message || 'Không thể lưu'));
                }
            })
            .catch(error => {
                console.error('Error saving:', error);
                alert('Có lỗi khi lưu');
            });
        }

        function previewPage() {
            if (!editor) {
                alert('Editor chưa được khởi tạo');
                return;
            }

            const html = editor.getHtml();
            const css = editor.getCss();
            
            const previewWindow = window.open('', '_blank');
            previewWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>Preview</title>
                    <style>${css}</style>
                </head>
                <body>
                    ${html}
                </body>
                </html>
            `);
            previewWindow.document.close();
        }
    </script>
</body>
</html>
