@php 
$layout_files = [
    'layout_1_column',
    'layout_2_columns_50_50', 
    'layout_2_columns_25_75',
    'layout_2_columns_75_25',
    'layout_3_columns',
    'layout_3_columns_50_25_25',
    'layout_3_columns_25_50_25', 
    'layout_3_columns_25_25_50',
    'layout_4_columns',
    'layout_5_columns'
];
@endphp

<div class="row g-2">
    @foreach ($layout_files as $file_name)
        <div class="col-md-12 position-relative">
            <div class="builder-blocks">
                @include('components.home_made_by_developer.' . $file_name)
            </div>
            <button onclick="add_block_html_content_by_select_from_offcanvas('{{ $file_name }}')" class="builder-block-placeholder" type="button">
                <i class="fi-rr-plus"></i>
            </button>
        </div>
    @endforeach
</div>

<style>
.layout-block {
    transition: all 0.3s ease;
    cursor: pointer;
}

.layout-block:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.builder-blocks {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
}

.builder-block-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.7);
    border: none;
    color: white;
    font-size: 2rem;
    opacity: 0;
    transition: all 0.3s ease;
    cursor: pointer;
    border-radius: 8px;
}

.builder-blocks:hover .builder-block-placeholder,
.builder-block-placeholder:hover {
    opacity: 1;
}

.builder-block-placeholder:hover {
    background: rgba(0,123,255,0.8);
}
</style>
