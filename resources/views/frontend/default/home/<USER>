<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>{{ $page->name ?? 'Trang xem trước' }}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS from GrapesJS -->
    @if($page->grapesjs_css)
        <style>
            {!! $page->grapesjs_css !!}
        </style>
    @endif
    
    <!-- Additional responsive styles -->
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .container-fluid {
            padding: 0;
        }
        
        /* Responsive utilities */
        @media (max-width: 768px) {
            .hero-section h1 {
                font-size: 2rem !important;
            }
            
            .hero-section p {
                font-size: 1rem !important;
            }
            
            .course-card {
                margin: 10px 0 !important;
            }
        }
        
        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
        }
        
        /* Button hover effects */
        .btn {
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        /* Image optimization */
        img {
            max-width: 100%;
            height: auto;
        }
        
        /* Form styling */
        .contact-form input,
        .contact-form textarea {
            transition: border-color 0.3s ease;
        }
        
        .contact-form input:focus,
        .contact-form textarea:focus {
            border-color: #007bff;
            outline: none;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }
    </style>
</head>
<body>
    <!-- GrapesJS Generated Content -->
    <div class="grapesjs-content">
        @if($page->grapesjs_html)
            {!! $page->grapesjs_html !!}
        @else
            <div class="container text-center py-5">
                <h1>Trang đang được cập nhật</h1>
                <p>Nội dung trang chưa được thiết lập. Vui lòng quay lại sau.</p>
                <a href="{{ url('/') }}" class="btn btn-primary">Về trang chủ</a>
            </div>
        @endif
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.3.1.slim.min.js"></script>
    
    <!-- Bootstrap JS -->
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.min.js"></script>
    
    <!-- Custom JavaScript for interactions -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Smooth scroll for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
            
            // Form submission handling
            document.querySelectorAll('.contact-form').forEach(form => {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    // Basic form validation
                    const name = form.querySelector('input[name="name"]');
                    const email = form.querySelector('input[name="email"]');
                    const message = form.querySelector('textarea[name="message"]');
                    
                    if (!name.value.trim()) {
                        alert('Vui lòng nhập họ và tên');
                        name.focus();
                        return;
                    }
                    
                    if (!email.value.trim() || !isValidEmail(email.value)) {
                        alert('Vui lòng nhập email hợp lệ');
                        email.focus();
                        return;
                    }
                    
                    if (!message.value.trim()) {
                        alert('Vui lòng nhập tin nhắn');
                        message.focus();
                        return;
                    }
                    
                    // Show success message (in real implementation, send to server)
                    alert('Cảm ơn bạn đã liên hệ! Chúng tôi sẽ phản hồi sớm nhất có thể.');
                    form.reset();
                });
            });
            
            // Course card interactions
            document.querySelectorAll('.course-card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                    this.style.boxShadow = '0 8px 25px rgba(0,0,0,0.15)';
                    this.style.transition = 'all 0.3s ease';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
                });
            });
            
            // Testimonial animations
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };
            
            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);
            
            document.querySelectorAll('.testimonial').forEach(testimonial => {
                testimonial.style.opacity = '0';
                testimonial.style.transform = 'translateY(30px)';
                testimonial.style.transition = 'all 0.6s ease';
                observer.observe(testimonial);
            });
        });
        
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }
        
        // Course registration handling
        function registerCourse(courseId) {
            // In real implementation, this would handle course registration
            alert('Chức năng đăng ký khóa học sẽ được triển khai sau.');
        }
        
        // Newsletter subscription
        function subscribeNewsletter(email) {
            if (!email || !isValidEmail(email)) {
                alert('Vui lòng nhập email hợp lệ');
                return;
            }
            
            // In real implementation, this would handle newsletter subscription
            alert('Cảm ơn bạn đã đăng ký nhận tin tức!');
        }
    </script>
</body>
</html>
