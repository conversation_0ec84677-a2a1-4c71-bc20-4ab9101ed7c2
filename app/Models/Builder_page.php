<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Builder_page extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'is_page_courses',
        'disable_bootstrap',
        'status',
        'is_permanent',
        'grapesjs_html',
        'grapesjs_css',
        'grapesjs_components',
        'grapesjs_styles',
        'use_grapesjs'
    ];

    protected $casts = [
        'grapesjs_components' => 'array',
        'grapesjs_styles' => 'array',
        'use_grapesjs' => 'boolean'
    ];
}
