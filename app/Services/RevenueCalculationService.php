<?php

namespace App\Services;

use App\Models\Payment_history;
use App\Models\OfflinePayment;
use App\Models\Enrollment;
use App\Models\Course;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RevenueCalculationService
{
    /**
     * Tính tổng doanh thu chính xác từ tất cả nguồn
     */
    public function calculateTotalRevenue()
    {
        try {
            $onlineRevenue = $this->getOnlinePaymentRevenue();
            $offlineRevenue = $this->getOfflinePaymentRevenue();
            $freeEnrollmentRevenue = $this->getFreeEnrollmentRevenue();

            $totalRevenue = $onlineRevenue + $offlineRevenue + $freeEnrollmentRevenue;

            Log::info('Revenue calculation completed', [
                'online_revenue' => $onlineRevenue,
                'offline_revenue' => $offlineRevenue,
                'free_enrollment_revenue' => $freeEnrollmentRevenue,
                'total_revenue' => $totalRevenue
            ]);

            return [
                'total' => $totalRevenue,
                'online' => $onlineRevenue,
                'offline' => $offlineRevenue,
                'free_enrollment' => $freeEnrollmentRevenue,
                'breakdown' => $this->getRevenueBreakdown()
            ];

        } catch (\Exception $e) {
            Log::error('Error calculating revenue', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return [
                'total' => 0,
                'online' => 0,
                'offline' => 0,
                'free_enrollment' => 0,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Doanh thu từ thanh toán online (payment_histories)
     */
    private function getOnlinePaymentRevenue()
    {
        return Payment_history::where('amount', '>', 0)
            ->sum('amount');
    }

    /**
     * Doanh thu từ thanh toán offline (offline_payments)
     */
    private function getOfflinePaymentRevenue()
    {
        return OfflinePayment::where('status', 1) // Chỉ tính những payment đã được approve
            ->where('total_amount', '>', 0)
            ->sum('total_amount');
    }

    /**
     * Doanh thu từ enrollment miễn phí (ước tính từ giá khóa học)
     * Chỉ tính những enrollment không có payment tương ứng
     */
    private function getFreeEnrollmentRevenue()
    {
        // Lấy các enrollment không có payment history tương ứng
        $freeEnrollments = DB::table('enrollments')
            ->leftJoin('payment_histories', function($join) {
                $join->on('enrollments.user_id', '=', 'payment_histories.user_id')
                     ->on('enrollments.course_id', '=', 'payment_histories.course_id');
            })
            ->leftJoin('offline_payments', function($join) {
                $join->on('enrollments.user_id', '=', 'offline_payments.user_id')
                     ->on('enrollments.course_id', '=', 'offline_payments.course_id')
                     ->where('offline_payments.status', '=', 1);
            })
            ->whereNull('payment_histories.id')
            ->whereNull('offline_payments.id')
            ->where('enrollments.enrollment_type', 'paid')
            ->select('enrollments.course_id')
            ->get();

        $revenue = 0;
        foreach ($freeEnrollments as $enrollment) {
            $course = Course::find($enrollment->course_id);
            if ($course && $course->price > 0) {
                $revenue += $course->price;
            }
        }

        return $revenue;
    }

    /**
     * Phân tích chi tiết doanh thu
     */
    public function getRevenueBreakdown()
    {
        return [
            'by_payment_method' => $this->getRevenueByPaymentMethod(),
            'by_course' => $this->getRevenueByCourse(),
            'by_month' => $this->getRevenueByMonth(),
            'affiliate_commission' => $this->getAffiliateCommission()
        ];
    }

    /**
     * Doanh thu theo phương thức thanh toán
     */
    private function getRevenueByPaymentMethod()
    {
        $online = DB::table('payment_histories')
            ->select('payment_type', DB::raw('SUM(amount) as total'))
            ->where('amount', '>', 0)
            ->groupBy('payment_type')
            ->get()
            ->pluck('total', 'payment_type')
            ->toArray();

        $offline = OfflinePayment::where('status', 1)
            ->where('total_amount', '>', 0)
            ->sum('total_amount');

        return [
            'online' => $online,
            'offline' => $offline,
            'total_online' => array_sum($online),
            'total_offline' => $offline
        ];
    }

    /**
     * Doanh thu theo khóa học
     */
    private function getRevenueByCourse()
    {
        // Doanh thu từ payment_histories
        $onlineRevenue = DB::table('payment_histories')
            ->join('courses', 'payment_histories.course_id', '=', 'courses.id')
            ->select('courses.title', 'courses.id', DB::raw('SUM(payment_histories.amount) as revenue'))
            ->where('payment_histories.amount', '>', 0)
            ->groupBy('courses.id', 'courses.title')
            ->get();

        // Doanh thu từ offline_payments
        $offlineRevenue = DB::table('offline_payments')
            ->join('courses', 'offline_payments.course_id', '=', 'courses.id')
            ->select('courses.title', 'courses.id', DB::raw('SUM(offline_payments.total_amount) as revenue'))
            ->where('offline_payments.status', 1)
            ->where('offline_payments.total_amount', '>', 0)
            ->groupBy('courses.id', 'courses.title')
            ->get();

        // Merge và tính tổng
        $courseRevenue = [];
        
        foreach ($onlineRevenue as $item) {
            $courseRevenue[$item->id] = [
                'title' => $item->title,
                'online' => $item->revenue,
                'offline' => 0,
                'total' => $item->revenue
            ];
        }

        foreach ($offlineRevenue as $item) {
            if (isset($courseRevenue[$item->id])) {
                $courseRevenue[$item->id]['offline'] = $item->revenue;
                $courseRevenue[$item->id]['total'] += $item->revenue;
            } else {
                $courseRevenue[$item->id] = [
                    'title' => $item->title,
                    'online' => 0,
                    'offline' => $item->revenue,
                    'total' => $item->revenue
                ];
            }
        }

        // Sắp xếp theo doanh thu giảm dần
        uasort($courseRevenue, function($a, $b) {
            return $b['total'] <=> $a['total'];
        });

        return $courseRevenue;
    }

    /**
     * Doanh thu theo tháng
     */
    private function getRevenueByMonth($months = 12)
    {
        $monthlyRevenue = [];

        for ($i = $months - 1; $i >= 0; $i--) {
            $month = now()->subMonths($i);
            $monthKey = $month->format('Y-m');
            
            // Doanh thu online
            $onlineRevenue = Payment_history::whereYear('created_at', $month->year)
                ->whereMonth('created_at', $month->month)
                ->where('amount', '>', 0)
                ->sum('amount');

            // Doanh thu offline
            $offlineRevenue = OfflinePayment::whereYear('created_at', $month->year)
                ->whereMonth('created_at', $month->month)
                ->where('status', 1)
                ->where('total_amount', '>', 0)
                ->sum('total_amount');

            $monthlyRevenue[$monthKey] = [
                'month' => $month->format('m/Y'),
                'online' => $onlineRevenue,
                'offline' => $offlineRevenue,
                'total' => $onlineRevenue + $offlineRevenue
            ];
        }

        return $monthlyRevenue;
    }

    /**
     * Tổng hoa hồng affiliate đã chi trả
     */
    private function getAffiliateCommission()
    {
        return OfflinePayment::where('status', 1)
            ->where('is_approve_affiliate', 1)
            ->sum('affiliate_amount');
    }

    /**
     * Tính doanh thu thực tế (sau khi trừ hoa hồng affiliate)
     */
    public function getNetRevenue()
    {
        $totalRevenue = $this->calculateTotalRevenue()['total'];
        $affiliateCommission = $this->getAffiliateCommission();
        
        return $totalRevenue - $affiliateCommission;
    }

    /**
     * Kiểm tra tính nhất quán dữ liệu
     */
    public function validateRevenueData()
    {
        $issues = [];

        // Kiểm tra enrollment không có payment
        $enrollmentsWithoutPayment = DB::table('enrollments')
            ->leftJoin('payment_histories', function($join) {
                $join->on('enrollments.user_id', '=', 'payment_histories.user_id')
                     ->on('enrollments.course_id', '=', 'payment_histories.course_id');
            })
            ->leftJoin('offline_payments', function($join) {
                $join->on('enrollments.user_id', '=', 'offline_payments.user_id')
                     ->on('enrollments.course_id', '=', 'offline_payments.course_id');
            })
            ->whereNull('payment_histories.id')
            ->whereNull('offline_payments.id')
            ->where('enrollments.enrollment_type', 'paid')
            ->count();

        if ($enrollmentsWithoutPayment > 0) {
            $issues[] = "Có {$enrollmentsWithoutPayment} enrollment được đánh dấu 'paid' nhưng không có payment tương ứng";
        }

        // Kiểm tra payment không có enrollment
        $paymentsWithoutEnrollment = DB::table('payment_histories')
            ->leftJoin('enrollments', function($join) {
                $join->on('payment_histories.user_id', '=', 'enrollments.user_id')
                     ->on('payment_histories.course_id', '=', 'enrollments.course_id');
            })
            ->whereNull('enrollments.id')
            ->count();

        if ($paymentsWithoutEnrollment > 0) {
            $issues[] = "Có {$paymentsWithoutEnrollment} payment không có enrollment tương ứng";
        }

        return $issues;
    }
}
