<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('builder_pages', function (Blueprint $table) {
            $table->longText('grapesjs_html')->nullable()->after('html');
            $table->longText('grapesjs_css')->nullable()->after('grapesjs_html');
            $table->json('grapesjs_components')->nullable()->after('grapesjs_css');
            $table->json('grapesjs_styles')->nullable()->after('grapesjs_components');
            $table->boolean('use_grapesjs')->default(false)->after('grapesjs_styles');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('builder_pages', function (Blueprint $table) {
            $table->dropColumn([
                'grapesjs_html',
                'grapesjs_css', 
                'grapesjs_components',
                'grapesjs_styles',
                'use_grapesjs'
            ]);
        });
    }
};
