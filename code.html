<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GrapesJS Layout Demo</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/grapesjs/0.21.7/css/grapes.min.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }

        .editor-container {
            width: 100%;
            height: 100vh;
        }

        .gjs-pn-panel {
            background: #2c3e50;
            color: white;
        }

        .gjs-pn-btn {
            background: #34495e;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 2px;
            cursor: pointer;
            border-radius: 4px;
            transition: background 0.3s;
        }

        .gjs-pn-btn:hover {
            background: #4a6741;
        }

        .gjs-pn-btn.gjs-pn-active {
            background: #27ae60;
        }

        /* Custom layout styles */
        .layout-row {
            display: flex;
            width: 100%;
            min-height: 50px;
            background: #ecf0f1;
            border: 2px dashed #bdc3c7;
            margin: 10px 0;
            padding: 10px;
            position: relative;
        }

        .layout-column {
            flex: 1;
            min-height: 40px;
            background: #e8f5e8;
            border: 1px solid #2ecc71;
            margin: 0 5px;
            padding: 15px;
            position: relative;
        }

        .layout-column:first-child {
            margin-left: 0;
        }

        .layout-column:last-child {
            margin-right: 0;
        }

        .layout-placeholder {
            color: #7f8c8d;
            font-style: italic;
            text-align: center;
            padding: 20px;
        }

        /* Responsive helpers */
        @media (max-width: 768px) {
            .layout-row {
                flex-direction: column;
            }

            .layout-column {
                margin: 5px 0;
            }
        }
    </style>
</head>
<body>
<div id="gjs" class="editor-container"></div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/grapesjs/0.21.7/grapes.min.js"></script>
<script>
    const editor = grapesjs.init({
        container: '#gjs',
        height: '100vh',
        width: 'auto',
        storageManager: false,

        // Panels configuration
        panels: {
            defaults: [
                {
                    id: 'layers',
                    el: '.panel__right',
                    resizable: {
                        maxDim: 350,
                        minDim: 200,
                        tc: 0,
                        cl: 1,
                        cr: 0,
                        bc: 0,
                        keyWidth: 'flex-basis',
                    },
                },
                {
                    id: 'panel-switcher',
                    el: '.panel__switcher',
                    buttons: [
                        {
                            id: 'show-layers',
                            active: true,
                            label: 'Layers',
                            command: 'show-layers',
                            togglable: false,
                        },
                        {
                            id: 'show-style',
                            active: true,
                            label: 'Styles',
                            command: 'show-styles',
                            togglable: false,
                        },
                        {
                            id: 'show-traits',
                            active: true,
                            label: 'Settings',
                            command: 'show-traits',
                            togglable: false,
                        }
                    ],
                }
            ]
        },

        // Layout blocks
        blockManager: {
            appendTo: '.blocks-container',
            blocks: [
                {
                    id: 'layout-row',
                    label: 'Row Layout',
                    category: 'Layout',
                    content: `
                            <div class="layout-row" data-gjs-highlightable="true" data-gjs-droppable="true">
                                <div class="layout-placeholder">Kéo thả các thành phần vào đây hoặc thêm cột</div>
                            </div>
                        `,
                    attributes: {
                        class: 'gjs-block-section'
                    }
                },
                {
                    id: 'layout-column',
                    label: 'Column',
                    category: 'Layout',
                    content: `
                            <div class="layout-column" data-gjs-highlightable="true" data-gjs-droppable="true">
                                <div class="layout-placeholder">Nội dung cột</div>
                            </div>
                        `,
                    attributes: {
                        class: 'gjs-block-section'
                    }
                },
                {
                    id: 'layout-2-columns',
                    label: '2 Columns',
                    category: 'Layout',
                    content: `
                            <div class="layout-row" data-gjs-highlightable="true" data-gjs-droppable="true">
                                <div class="layout-column" data-gjs-highlightable="true" data-gjs-droppable="true">
                                    <div class="layout-placeholder">Cột 1</div>
                                </div>
                                <div class="layout-column" data-gjs-highlightable="true" data-gjs-droppable="true">
                                    <div class="layout-placeholder">Cột 2</div>
                                </div>
                            </div>
                        `,
                    attributes: {
                        class: 'gjs-block-section'
                    }
                },
                {
                    id: 'layout-3-columns',
                    label: '3 Columns',
                    category: 'Layout',
                    content: `
                            <div class="layout-row" data-gjs-highlightable="true" data-gjs-droppable="true">
                                <div class="layout-column" data-gjs-highlightable="true" data-gjs-droppable="true">
                                    <div class="layout-placeholder">Cột 1</div>
                                </div>
                                <div class="layout-column" data-gjs-highlightable="true" data-gjs-droppable="true">
                                    <div class="layout-placeholder">Cột 2</div>
                                </div>
                                <div class="layout-column" data-gjs-highlightable="true" data-gjs-droppable="true">
                                    <div class="layout-placeholder">Cột 3</div>
                                </div>
                            </div>
                        `,
                    attributes: {
                        class: 'gjs-block-section'
                    }
                },
                {
                    id: 'text-block',
                    label: 'Text',
                    category: 'Basic',
                    content: '<div data-gjs-type="text">Nhập văn bản của bạn ở đây...</div>',
                    attributes: {
                        class: 'gjs-block-section'
                    }
                },
                {
                    id: 'image-block',
                    label: 'Image',
                    category: 'Basic',
                    content: '<img src="https://via.placeholder.com/300x200" alt="Placeholder Image" style="width: 100%; height: auto;">',
                    attributes: {
                        class: 'gjs-block-section'
                    }
                }
            ]
        },

        // Device manager for responsive design
        deviceManager: {
            devices: [
                {
                    name: 'Desktop',
                    width: '',
                },
                {
                    name: 'Tablet',
                    width: '768px',
                    widthMedia: '992px',
                },
                {
                    name: 'Mobile',
                    width: '320px',
                    widthMedia: '768px',
                }
            ]
        },

        // Custom CSS rules
        canvas: {
            styles: [`
                    .layout-row {
                        display: flex;
                        width: 100%;
                        min-height: 50px;
                        background: #ecf0f1;
                        border: 2px dashed #bdc3c7;
                        margin: 10px 0;
                        padding: 10px;
                        position: relative;
                    }

                    .layout-column {
                        flex: 1;
                        min-height: 40px;
                        background: #e8f5e8;
                        border: 1px solid #2ecc71;
                        margin: 0 5px;
                        padding: 15px;
                        position: relative;
                    }

                    .layout-column:first-child {
                        margin-left: 0;
                    }

                    .layout-column:last-child {
                        margin-right: 0;
                    }

                    .layout-placeholder {
                        color: #7f8c8d;
                        font-style: italic;
                        text-align: center;
                        padding: 20px;
                    }

                    @media (max-width: 768px) {
                        .layout-row {
                            flex-direction: column;
                        }

                        .layout-column {
                            margin: 5px 0;
                        }
                    }
                `]
        }
    });

    // Add custom commands for layout manipulation
    editor.Commands.add('add-column', {
        run: function(editor, sender) {
            const selected = editor.getSelected();
            if (selected && selected.is('layout-row')) {
                const newColumn = editor.DomComponents.addComponent({
                    tagName: 'div',
                    classes: ['layout-column'],
                    attributes: {
                        'data-gjs-highlightable': 'true',
                        'data-gjs-droppable': 'true'
                    },
                    content: '<div class="layout-placeholder">Cột mới</div>'
                });
                selected.append(newColumn);
            }
        }
    });

    editor.Commands.add('remove-column', {
        run: function(editor, sender) {
            const selected = editor.getSelected();
            if (selected && selected.is('layout-column')) {
                const parent = selected.parent();
                if (parent && parent.components().length > 1) {
                    selected.remove();
                } else {
                    alert('Không thể xóa cột cuối cùng!');
                }
            }
        }
    });

    // Context menu for layout elements
    editor.on('component:selected', function(component) {
        const toolbar = component.get('toolbar');

        if (component.get('classes').includes('layout-row')) {
            toolbar.push({
                attributes: {class: 'fa fa-plus'},
                command: 'add-column',
                label: 'Thêm cột'
            });
        }

        if (component.get('classes').includes('layout-column')) {
            toolbar.push({
                attributes: {class: 'fa fa-trash'},
                command: 'remove-column',
                label: 'Xóa cột'
            });
        }
    });

    // Add some sample content
    editor.setComponents(`
            <div class="layout-row">
                <div class="layout-column">
                    <h2>Cột 1</h2>
                    <p>Đây là nội dung của cột đầu tiên. Bạn có thể thêm văn bản, hình ảnh và các thành phần khác vào đây.</p>
                </div>
                <div class="layout-column">
                    <h2>Cột 2</h2>
                    <p>Đây là nội dung của cột thứ hai. Layout này hoàn toàn responsive và sẽ tự động điều chỉnh trên các thiết bị khác nhau.</p>
                </div>
            </div>

            <div class="layout-row">
                <div class="layout-column">
                    <h3>Cột đơn</h3>
                    <p>Đây là một layout row với chỉ một cột. Bạn có thể thêm nhiều cột hơn bằng cách click vào row và chọn "Thêm cột".</p>
                </div>
            </div>
        `);

    // Auto-save functionality (in memory)
    let autoSaveInterval;
    const startAutoSave = () => {
        autoSaveInterval = setInterval(() => {
            const content = editor.getHtml();
            const css = editor.getCss();
            console.log('Auto-saving...', {content, css});
        }, 30000); // Auto-save every 30 seconds
    };

    startAutoSave();

    // Cleanup on page unload
    window.addEventListener('beforeunload', () => {
        if (autoSaveInterval) {
            clearInterval(autoSaveInterval);
        }
    });

    // Add keyboard shortcuts
    document.addEventListener('keydown', (e) => {
        if (e.ctrlKey || e.metaKey) {
            switch(e.key) {
                case 's':
                    e.preventDefault();
                    console.log('Saved:', {
                        html: editor.getHtml(),
                        css: editor.getCss()
                    });
                    alert('Đã lưu thành công!');
                    break;
                case 'z':
                    e.preventDefault();
                    editor.UndoManager.undo();
                    break;
                case 'y':
                    e.preventDefault();
                    editor.UndoManager.redo();
                    break;
            }
        }
    });

    console.log('GrapesJS Layout Demo initialized successfully!');
</script>
</body>
</html>
